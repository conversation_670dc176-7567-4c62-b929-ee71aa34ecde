#!/usr/bin/env python3
"""
Parallel Execution System for Freqtrade Bots
Manages up to 100 concurrent bot instances with proper resource allocation and monitoring.
"""

import asyncio
import multiprocessing
import psutil
import signal
import time
import json
import logging
import os
import uuid
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BotStatus(Enum):
    """Bot instance status"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    CRASHED = "crashed"


@dataclass
class BotInstance:
    """Bot instance configuration and state"""
    id: str
    name: str
    config: Dict[str, Any]
    status: BotStatus = BotStatus.STOPPED
    process_id: Optional[int] = None
    start_time: Optional[float] = None
    stop_time: Optional[float] = None
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    error_message: Optional[str] = None
    restart_count: int = 0
    last_heartbeat: Optional[float] = None


class ResourceManager:
    """Manages system resources for bot instances"""
    
    def __init__(self, max_instances: int = 100):
        self.max_instances = max_instances
        self.cpu_limit_per_bot = 2.0  # Max CPU percentage per bot
        self.memory_limit_per_bot = 512  # Max memory MB per bot
        
        # System limits
        self.total_cpu_cores = multiprocessing.cpu_count()
        self.total_memory_gb = psutil.virtual_memory().total / (1024**3)
        
        logger.info(f"Resource Manager initialized: {self.total_cpu_cores} CPU cores, {self.total_memory_gb:.1f}GB RAM")
    
    def can_start_instance(self, current_instances: int) -> Tuple[bool, str]:
        """Check if we can start another instance"""
        if current_instances >= self.max_instances:
            return False, f"Maximum instances limit reached ({self.max_instances})"
        
        # Check system resources
        cpu_percent = psutil.cpu_percent(interval=1)
        memory_percent = psutil.virtual_memory().percent
        
        if cpu_percent > 80:
            return False, f"High CPU usage: {cpu_percent:.1f}%"
        
        if memory_percent > 80:
            return False, f"High memory usage: {memory_percent:.1f}%"
        
        return True, "Resources available"
    
    def get_resource_allocation(self, instance_count: int) -> Dict[str, float]:
        """Calculate resource allocation per instance"""
        if instance_count == 0:
            return {"cpu_limit": self.cpu_limit_per_bot, "memory_limit": self.memory_limit_per_bot}
        
        # Dynamic allocation based on instance count
        cpu_per_instance = min(self.cpu_limit_per_bot, (self.total_cpu_cores * 80) / instance_count)
        memory_per_instance = min(self.memory_limit_per_bot, (self.total_memory_gb * 1024 * 80) / instance_count)
        
        return {
            "cpu_limit": cpu_per_instance,
            "memory_limit": memory_per_instance
        }
    
    def monitor_instance_resources(self, process_id: int) -> Dict[str, float]:
        """Monitor resource usage of a specific process"""
        try:
            process = psutil.Process(process_id)
            cpu_percent = process.cpu_percent()
            memory_mb = process.memory_info().rss / (1024 * 1024)
            
            return {
                "cpu_usage": cpu_percent,
                "memory_usage": memory_mb,
                "status": process.status()
            }
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return {"cpu_usage": 0.0, "memory_usage": 0.0, "status": "not_found"}


class ProcessManager:
    """Manages bot processes"""
    
    def __init__(self, work_dir: str = "/tmp/freqtrade_bots"):
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)
        self.processes: Dict[str, multiprocessing.Process] = {}
        
        logger.info(f"Process Manager initialized with work directory: {self.work_dir}")
    
    def create_bot_process(self, bot_instance: BotInstance) -> multiprocessing.Process:
        """Create a new bot process"""
        
        def bot_worker(bot_id: str, config: Dict[str, Any], work_dir: str):
            """Worker function that runs in separate process"""
            import sys
            import os
            
            # Set up process-specific logging
            process_logger = logging.getLogger(f"bot_{bot_id}")
            handler = logging.FileHandler(f"{work_dir}/bot_{bot_id}.log")
            handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
            process_logger.addHandler(handler)
            process_logger.setLevel(logging.INFO)
            
            try:
                # Import bot modules (would be actual imports in real implementation)
                # from ai_enhanced_bot import AIEnhancedFreqtradeBot
                # from bisq_integration import BisqIntegrationModule
                
                process_logger.info(f"Starting bot {bot_id} with config: {config}")
                
                # Create bot instance
                # bot = AIEnhancedFreqtradeBot(config)
                
                # Initialize Bisq integration if enabled
                # if config.get('use_bisq', False):
                #     bisq = BisqIntegrationModule(config.get('bisq_config', {}))
                #     await bisq.initialize()
                
                # Simulate bot execution
                start_time = time.time()
                iteration = 0
                
                while True:
                    iteration += 1
                    
                    # Simulate trading logic
                    process_logger.info(f"Bot {bot_id} iteration {iteration}")
                    
                    # Simulate some work
                    time.sleep(config.get('update_interval', 30))
                    
                    # Write heartbeat
                    heartbeat_file = f"{work_dir}/bot_{bot_id}_heartbeat.json"
                    with open(heartbeat_file, 'w') as f:
                        json.dump({
                            'timestamp': time.time(),
                            'iteration': iteration,
                            'uptime': time.time() - start_time,
                            'status': 'running'
                        }, f)
                    
                    # Check for stop signal
                    stop_file = f"{work_dir}/bot_{bot_id}_stop.signal"
                    if os.path.exists(stop_file):
                        process_logger.info(f"Stop signal received for bot {bot_id}")
                        os.remove(stop_file)
                        break
                        
            except Exception as e:
                process_logger.error(f"Bot {bot_id} crashed: {e}")
                # Write error status
                error_file = f"{work_dir}/bot_{bot_id}_error.json"
                with open(error_file, 'w') as f:
                    json.dump({
                        'timestamp': time.time(),
                        'error': str(e),
                        'status': 'error'
                    }, f)
                raise
            finally:
                process_logger.info(f"Bot {bot_id} stopped")
        
        # Create process
        process = multiprocessing.Process(
            target=bot_worker,
            args=(bot_instance.id, bot_instance.config, str(self.work_dir)),
            name=f"bot_{bot_instance.id}"
        )
        
        return process
    
    def start_bot(self, bot_instance: BotInstance) -> bool:
        """Start a bot process"""
        try:
            process = self.create_bot_process(bot_instance)
            process.start()
            
            self.processes[bot_instance.id] = process
            bot_instance.process_id = process.pid
            bot_instance.start_time = time.time()
            bot_instance.status = BotStatus.RUNNING
            
            logger.info(f"Started bot {bot_instance.id} with PID {process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start bot {bot_instance.id}: {e}")
            bot_instance.status = BotStatus.ERROR
            bot_instance.error_message = str(e)
            return False
    
    def stop_bot(self, bot_instance: BotInstance) -> bool:
        """Stop a bot process"""
        try:
            if bot_instance.id not in self.processes:
                logger.warning(f"Bot {bot_instance.id} process not found")
                return False
            
            process = self.processes[bot_instance.id]
            
            # Send stop signal via file
            stop_file = self.work_dir / f"bot_{bot_instance.id}_stop.signal"
            stop_file.touch()
            
            # Wait for graceful shutdown
            process.join(timeout=10)
            
            if process.is_alive():
                logger.warning(f"Bot {bot_instance.id} did not stop gracefully, terminating")
                process.terminate()
                process.join(timeout=5)
                
                if process.is_alive():
                    logger.error(f"Bot {bot_instance.id} did not terminate, killing")
                    process.kill()
                    process.join()
            
            del self.processes[bot_instance.id]
            bot_instance.status = BotStatus.STOPPED
            bot_instance.stop_time = time.time()
            bot_instance.process_id = None
            
            logger.info(f"Stopped bot {bot_instance.id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop bot {bot_instance.id}: {e}")
            return False
    
    def get_bot_heartbeat(self, bot_id: str) -> Optional[Dict]:
        """Get bot heartbeat information"""
        try:
            heartbeat_file = self.work_dir / f"bot_{bot_id}_heartbeat.json"
            if heartbeat_file.exists():
                with open(heartbeat_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to read heartbeat for bot {bot_id}: {e}")
        return None
    
    def cleanup_bot_files(self, bot_id: str):
        """Clean up bot-related files"""
        files_to_remove = [
            f"bot_{bot_id}.log",
            f"bot_{bot_id}_heartbeat.json",
            f"bot_{bot_id}_error.json",
            f"bot_{bot_id}_stop.signal"
        ]
        
        for filename in files_to_remove:
            file_path = self.work_dir / filename
            if file_path.exists():
                try:
                    file_path.unlink()
                except Exception as e:
                    logger.warning(f"Failed to remove {filename}: {e}")


class ParallelExecutionSystem:
    """Main system for managing parallel bot execution"""
    
    def __init__(self, max_instances: int = 100):
        self.max_instances = max_instances
        self.instances: Dict[str, BotInstance] = {}
        
        # Managers
        self.resource_manager = ResourceManager(max_instances)
        self.process_manager = ProcessManager()
        
        # Monitoring
        self.monitoring_task = None
        self.is_monitoring = False
        
        logger.info(f"Parallel Execution System initialized for {max_instances} instances")
    
    def create_bot_instance(self, name: str, config: Dict[str, Any]) -> str:
        """Create a new bot instance"""
        bot_id = str(uuid.uuid4())[:8]
        
        instance = BotInstance(
            id=bot_id,
            name=name,
            config=config.copy()
        )
        
        self.instances[bot_id] = instance
        logger.info(f"Created bot instance {bot_id} ({name})")
        
        return bot_id
    
    def start_bot(self, bot_id: str) -> Tuple[bool, str]:
        """Start a bot instance"""
        if bot_id not in self.instances:
            return False, f"Bot {bot_id} not found"
        
        instance = self.instances[bot_id]
        
        if instance.status == BotStatus.RUNNING:
            return False, f"Bot {bot_id} is already running"
        
        # Check resources
        running_count = sum(1 for inst in self.instances.values() if inst.status == BotStatus.RUNNING)
        can_start, reason = self.resource_manager.can_start_instance(running_count)
        
        if not can_start:
            return False, reason
        
        # Start the bot
        instance.status = BotStatus.STARTING
        success = self.process_manager.start_bot(instance)
        
        if success:
            return True, f"Bot {bot_id} started successfully"
        else:
            return False, f"Failed to start bot {bot_id}: {instance.error_message}"
    
    def stop_bot(self, bot_id: str) -> Tuple[bool, str]:
        """Stop a bot instance"""
        if bot_id not in self.instances:
            return False, f"Bot {bot_id} not found"
        
        instance = self.instances[bot_id]
        
        if instance.status != BotStatus.RUNNING:
            return False, f"Bot {bot_id} is not running"
        
        instance.status = BotStatus.STOPPING
        success = self.process_manager.stop_bot(instance)
        
        if success:
            return True, f"Bot {bot_id} stopped successfully"
        else:
            return False, f"Failed to stop bot {bot_id}"
    
    def remove_bot(self, bot_id: str) -> Tuple[bool, str]:
        """Remove a bot instance"""
        if bot_id not in self.instances:
            return False, f"Bot {bot_id} not found"
        
        instance = self.instances[bot_id]
        
        # Stop if running
        if instance.status == BotStatus.RUNNING:
            self.stop_bot(bot_id)
        
        # Clean up files
        self.process_manager.cleanup_bot_files(bot_id)
        
        # Remove from instances
        del self.instances[bot_id]
        
        logger.info(f"Removed bot instance {bot_id}")
        return True, f"Bot {bot_id} removed successfully"
    
    def get_bot_status(self, bot_id: str) -> Optional[Dict]:
        """Get bot status"""
        if bot_id not in self.instances:
            return None
        
        instance = self.instances[bot_id]
        
        # Get heartbeat
        heartbeat = self.process_manager.get_bot_heartbeat(bot_id)
        
        # Get resource usage
        resource_usage = {}
        if instance.process_id:
            resource_usage = self.resource_manager.monitor_instance_resources(instance.process_id)
            instance.cpu_usage = resource_usage.get('cpu_usage', 0.0)
            instance.memory_usage = resource_usage.get('memory_usage', 0.0)
        
        status = asdict(instance)
        status['heartbeat'] = heartbeat
        status['resource_usage'] = resource_usage
        
        return status
    
    def list_bots(self) -> List[Dict]:
        """List all bot instances"""
        return [self.get_bot_status(bot_id) for bot_id in self.instances.keys()]
    
    def get_system_status(self) -> Dict:
        """Get overall system status"""
        running_bots = [inst for inst in self.instances.values() if inst.status == BotStatus.RUNNING]
        
        total_cpu = sum(inst.cpu_usage for inst in running_bots)
        total_memory = sum(inst.memory_usage for inst in running_bots)
        
        system_cpu = psutil.cpu_percent()
        system_memory = psutil.virtual_memory().percent
        
        return {
            'total_instances': len(self.instances),
            'running_instances': len(running_bots),
            'max_instances': self.max_instances,
            'system_cpu_percent': system_cpu,
            'system_memory_percent': system_memory,
            'bots_cpu_usage': total_cpu,
            'bots_memory_usage': total_memory,
            'uptime': time.time() - getattr(self, 'start_time', time.time())
        }
    
    async def start_monitoring(self):
        """Start monitoring task"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.start_time = time.time()
        
        async def monitor_loop():
            while self.is_monitoring:
                try:
                    await self._monitor_instances()
                    await asyncio.sleep(30)  # Monitor every 30 seconds
                except Exception as e:
                    logger.error(f"Monitoring error: {e}")
                    await asyncio.sleep(5)
        
        self.monitoring_task = asyncio.create_task(monitor_loop())
        logger.info("Started monitoring task")
    
    async def stop_monitoring(self):
        """Stop monitoring task"""
        self.is_monitoring = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Stopped monitoring task")
    
    async def _monitor_instances(self):
        """Monitor all instances"""
        for bot_id, instance in self.instances.items():
            if instance.status == BotStatus.RUNNING:
                # Check heartbeat
                heartbeat = self.process_manager.get_bot_heartbeat(bot_id)
                if heartbeat:
                    instance.last_heartbeat = heartbeat['timestamp']
                    
                    # Check if heartbeat is stale (no update in 2 minutes)
                    if time.time() - instance.last_heartbeat > 120:
                        logger.warning(f"Bot {bot_id} heartbeat is stale, marking as crashed")
                        instance.status = BotStatus.CRASHED
                        
                        # Try to restart if restart count is low
                        if instance.restart_count < 3:
                            logger.info(f"Attempting to restart bot {bot_id}")
                            self.stop_bot(bot_id)
                            await asyncio.sleep(5)
                            success, message = self.start_bot(bot_id)
                            if success:
                                instance.restart_count += 1
                                logger.info(f"Restarted bot {bot_id} (restart #{instance.restart_count})")
                            else:
                                logger.error(f"Failed to restart bot {bot_id}: {message}")
                
                # Update resource usage
                if instance.process_id:
                    resource_usage = self.resource_manager.monitor_instance_resources(instance.process_id)
                    instance.cpu_usage = resource_usage.get('cpu_usage', 0.0)
                    instance.memory_usage = resource_usage.get('memory_usage', 0.0)
                    
                    # Check if process still exists
                    if resource_usage.get('status') == 'not_found':
                        logger.warning(f"Bot {bot_id} process not found, marking as crashed")
                        instance.status = BotStatus.CRASHED
                        instance.process_id = None
    
    async def shutdown(self):
        """Shutdown the system"""
        logger.info("Shutting down Parallel Execution System")
        
        # Stop monitoring
        await self.stop_monitoring()
        
        # Stop all running bots
        running_bots = [bot_id for bot_id, inst in self.instances.items() if inst.status == BotStatus.RUNNING]
        
        for bot_id in running_bots:
            logger.info(f"Stopping bot {bot_id}")
            self.stop_bot(bot_id)
        
        # Wait for all to stop
        await asyncio.sleep(5)
        
        logger.info("Parallel Execution System shutdown complete")


# Demo and testing functions
async def demo_parallel_execution():
    """Demo the parallel execution system"""
    
    print("=== Parallel Execution System Demo ===")
    
    # Create system
    system = ParallelExecutionSystem(max_instances=10)  # Limit for demo
    
    # Start monitoring
    await system.start_monitoring()
    
    print("\n1. Creating bot instances...")
    
    # Create multiple bot instances
    bot_configs = [
        {"name": "BTC_Bot_1", "trading_pair": "BTC/USDT", "update_interval": 10},
        {"name": "ETH_Bot_1", "trading_pair": "ETH/USDT", "update_interval": 15},
        {"name": "BTC_Bot_2", "trading_pair": "BTC/EUR", "update_interval": 20},
    ]
    
    bot_ids = []
    for i, config in enumerate(bot_configs):
        bot_id = system.create_bot_instance(f"Bot_{i+1}", config)
        bot_ids.append(bot_id)
        print(f"  Created bot {bot_id}: {config['name']}")
    
    print(f"\n2. System status before starting bots:")
    status = system.get_system_status()
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    print(f"\n3. Starting bots...")
    for bot_id in bot_ids:
        success, message = system.start_bot(bot_id)
        print(f"  {bot_id}: {'✓' if success else '✗'} {message}")
    
    print(f"\n4. Waiting for bots to initialize...")
    await asyncio.sleep(15)
    
    print(f"\n5. Bot statuses:")
    for bot_id in bot_ids:
        status = system.get_bot_status(bot_id)
        if status:
            print(f"  {bot_id} ({status['name']}):")
            print(f"    Status: {status['status']}")
            print(f"    CPU: {status['cpu_usage']:.1f}%")
            print(f"    Memory: {status['memory_usage']:.1f}MB")
            if status['heartbeat']:
                print(f"    Last heartbeat: {status['heartbeat']['iteration']} iterations")
    
    print(f"\n6. System status with running bots:")
    status = system.get_system_status()
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    print(f"\n7. Stopping bots...")
    for bot_id in bot_ids:
        success, message = system.stop_bot(bot_id)
        print(f"  {bot_id}: {'✓' if success else '✗'} {message}")
    
    print(f"\n8. Cleaning up...")
    for bot_id in bot_ids:
        system.remove_bot(bot_id)
    
    # Shutdown system
    await system.shutdown()
    
    print("\n=== Demo Complete ===")


if __name__ == "__main__":
    asyncio.run(demo_parallel_execution())

