#!/usr/bin/env python3
"""
Demo script for testing Telegram bot functionality without actual Telegram token.
"""

import asyncio
import json
from telegram_bot import <PERSON><PERSON><PERSON><PERSON><PERSON>

async def demo_bot_manager():
    """Demo the bot manager functionality"""
    
    print("=== Bot Manager Demo ===")
    
    # Create bot manager
    manager = BotManager()
    
    # Default configuration
    config = {
        'exchange': 'binance',
        'trading_pair': 'BTC/USDT',
        'timeframe': '1m',
        'initial_balance': 1000.0,
        'sandbox': True,
        'short_window': 10,
        'long_window': 30,
        'rsi_period': 14,
        'ai_config': {
            'learning_rate': 0.01,
            'exploration_rate': 0.1
        }
    }
    
    # Test creating bots
    print("\n1. Creating bots...")
    success1 = manager.create_bot("demo_bot_1", config)
    print(f"Created demo_bot_1: {success1}")
    
    config2 = config.copy()
    config2['trading_pair'] = 'ETH/USDT'
    success2 = manager.create_bot("demo_bot_2", config2)
    print(f"Created demo_bot_2: {success2}")
    
    # List bots
    print("\n2. Listing bots...")
    bots = manager.list_bots()
    print(f"Available bots: {bots}")
    
    # Get status before starting
    print("\n3. Bot status (before starting)...")
    for bot_id in bots:
        status = manager.get_bot_status(bot_id)
        print(f"{bot_id} status: Running={status.get('is_running', False)}, "
              f"Task={status.get('task_running', False)}, "
              f"Balance=${status.get('balance', 0):.2f}")
    
    # Start a bot
    print("\n4. Starting demo_bot_1...")
    start_success = await manager.start_bot("demo_bot_1")
    print(f"Started demo_bot_1: {start_success}")
    
    # Wait a bit and check status
    print("\n5. Waiting 10 seconds...")
    await asyncio.sleep(10)
    
    print("\n6. Bot status (after starting)...")
    status = manager.get_bot_status("demo_bot_1")
    if status:
        print(f"demo_bot_1 status:")
        print(f"  Running: {status.get('is_running', False)}")
        print(f"  Task Running: {status.get('task_running', False)}")
        print(f"  Trading Pair: {status.get('trading_pair', 'N/A')}")
        print(f"  Current Price: ${status.get('current_price', 0):.2f}")
        print(f"  Balance: ${status.get('balance', 0):.2f}")
        print(f"  Total Value: ${status.get('total_value', 0):.2f}")
        print(f"  Total Trades: {status.get('total_trades', 0)}")
        
        # AI status
        ai_status = status.get('ai_status', {})
        if ai_status:
            print(f"  AI Regime: {ai_status.get('last_analysis', {}).get('regime', 'unknown')}")
            print(f"  AI Signal: {ai_status.get('last_decision', {}).get('signal', 'none')}")
    
    # Stop the bot
    print("\n7. Stopping demo_bot_1...")
    stop_success = await manager.stop_bot("demo_bot_1")
    print(f"Stopped demo_bot_1: {stop_success}")
    
    # Final status
    print("\n8. Final status...")
    for bot_id in bots:
        status = manager.get_bot_status(bot_id)
        print(f"{bot_id}: Running={status.get('is_running', False)}, "
              f"Task={status.get('task_running', False)}")
    
    print("\n=== Demo Complete ===")

if __name__ == "__main__":
    asyncio.run(demo_bot_manager())

