#!/usr/bin/env python3
"""
Quick test script for the Freqtrade bot core functionality
"""

import asyncio
import json
from freqtrade_bot_core import FreqtradeBotCore

async def quick_test():
    """Quick test of bot functionality"""
    
    # Load configuration
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # Create bot instance
    bot = FreqtradeBotCore(config)
    
    print("=== Bot Initialization Test ===")
    print(f"Trading pair: {bot.trading_pair}")
    print(f"Exchange: {bot.exchange_name}")
    print(f"Initial balance: {bot.initial_balance}")
    
    # Test market data fetching
    print("\n=== Market Data Test ===")
    price = await bot.fetch_market_data()
    print(f"Current price: {price}")
    
    # Test strategy
    print("\n=== Strategy Test ===")
    # Simulate some price data
    import pandas as pd
    import numpy as np
    
    # Generate mock price data for testing
    base_price = 50000
    prices = []
    for i in range(50):
        price_change = np.random.uniform(-0.01, 0.01)
        base_price *= (1 + price_change)
        prices.append(base_price)
    
    price_series = pd.Series(prices)
    bot.price_history = price_series
    
    should_buy, buy_reason = bot.strategy.should_buy(price_series)
    print(f"Should buy: {should_buy}")
    print(f"Reason: {buy_reason}")
    
    # Test order execution
    print("\n=== Order Execution Test ===")
    if bot.execute_buy_order(price or 50000, 0.01):
        print("Buy order executed successfully")
        
        # Test sell order
        if bot.execute_sell_order((price or 50000) * 1.02, 0.01):
            print("Sell order executed successfully")
    
    # Get final status
    print("\n=== Final Status ===")
    status = bot.get_status()
    for key, value in status.items():
        print(f"{key}: {value}")

if __name__ == "__main__":
    asyncio.run(quick_test())

