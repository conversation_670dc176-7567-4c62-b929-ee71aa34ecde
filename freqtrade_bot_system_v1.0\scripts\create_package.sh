#!/bin/bash
# Freqtrade Bot System - Package Creation Script
# Creates a complete deployment package

set -e

PACKAGE_NAME="freqtrade_bot_system_v1.0"
PACKAGE_DIR="/tmp/$PACKAGE_NAME"
ARCHIVE_NAME="$PACKAGE_NAME.tar.gz"

echo "=== Creating Deployment Package ==="

# Clean up any existing package
rm -rf "$PACKAGE_DIR"
rm -f "/tmp/$ARCHIVE_NAME"

# Create package directory
mkdir -p "$PACKAGE_DIR"

# Copy all system files
echo "Copying system files..."
cp *.py "$PACKAGE_DIR/"
cp *.md "$PACKAGE_DIR/"
cp *.pdf "$PACKAGE_DIR/" 2>/dev/null || echo "No PDF files found"
cp *.sh "$PACKAGE_DIR/"
cp *.json "$PACKAGE_DIR/" 2>/dev/null || echo "No JSON files found"

# Create package structure
mkdir -p "$PACKAGE_DIR"/{config,docs,scripts,tests}

# Move files to appropriate directories
mv "$PACKAGE_DIR"/documentation.* "$PACKAGE_DIR/docs/"
mv "$PACKAGE_DIR"/test_suite.py "$PACKAGE_DIR/tests/"
mv "$PACKAGE_DIR"/*.sh "$PACKAGE_DIR/scripts/"
mv "$PACKAGE_DIR"/*.json "$PACKAGE_DIR/config/" 2>/dev/null || true

# Create package info
cat > "$PACKAGE_DIR/PACKAGE_INFO.txt" << EOF
Freqtrade Bot System v1.0
========================

Package Contents:
- Core Python modules for trading bot system
- AI agent framework with machine learning capabilities
- Telegram bot integration for remote control
- Bisq decentralized exchange integration
- Parallel execution system for up to 100 bot instances
- Comprehensive documentation and user guides
- Deployment and configuration scripts
- Complete test suite

Installation:
1. Extract this package
2. Run: ./scripts/deploy.sh
3. Follow the configuration instructions in docs/

For detailed instructions, see docs/README.md

Created: $(date)
Version: 1.0
Platform: Linux (Ubuntu 22.04+ recommended)
EOF

# Create installation instructions
cat > "$PACKAGE_DIR/INSTALL.txt" << EOF
Quick Installation Instructions
==============================

1. Extract the package:
   tar -xzf $ARCHIVE_NAME
   cd $PACKAGE_NAME

2. Run the deployment script:
   ./scripts/deploy.sh

3. Follow the prompts to configure the system

4. Edit configuration files:
   ~/.freqtrade_bot_system/config/master_config.json
   ~/.freqtrade_bot_system/config/.env

5. Start the system:
   cd ~/freqtrade_bot_system
   ./start_system.sh

For complete documentation, see docs/documentation.pdf
EOF

# Create archive
echo "Creating archive..."
cd /tmp
tar -czf "$ARCHIVE_NAME" "$PACKAGE_NAME"

# Calculate checksums
echo "Calculating checksums..."
md5sum "$ARCHIVE_NAME" > "$ARCHIVE_NAME.md5"
sha256sum "$ARCHIVE_NAME" > "$ARCHIVE_NAME.sha256"

# Display package info
echo ""
echo "=== Package Created Successfully ==="
echo "Package: /tmp/$ARCHIVE_NAME"
echo "Size: $(du -h /tmp/$ARCHIVE_NAME | cut -f1)"
echo "MD5: $(cat /tmp/$ARCHIVE_NAME.md5 | cut -d' ' -f1)"
echo "SHA256: $(cat /tmp/$ARCHIVE_NAME.sha256 | cut -d' ' -f1)"
echo ""
echo "Package contents:"
tar -tzf "/tmp/$ARCHIVE_NAME" | head -20
echo "... (and more)"
echo ""
echo "To install: tar -xzf $ARCHIVE_NAME && cd $PACKAGE_NAME && ./scripts/deploy.sh"

