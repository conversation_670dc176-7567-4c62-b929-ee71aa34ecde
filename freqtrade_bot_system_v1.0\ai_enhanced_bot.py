#!/usr/bin/env python3
"""
Enhanced Freqtrade Bot with AI Agent Integration
Combines the core bot functionality with intelligent AI decision-making.
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Optional
import pandas as pd

from freqtrade_bot_core import FreqtradeBotCore, TradingStrategy
from ai_agent import AITradingAgent, TradingSignal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AIEnhancedTradingStrategy(TradingStrategy):
    """
    Enhanced trading strategy that uses AI agent for decision making.
    """
    
    def __init__(self, ai_agent: AITradingAgent, **kwargs):
        super().__init__(**kwargs)
        self.ai_agent = ai_agent
        
    def should_buy(self, prices: pd.Series, current_price: float, balance: float) -> tuple[bool, str]:
        """AI-enhanced buy decision"""
        
        # Get AI decision
        decision = self.ai_agent.make_trading_decision(
            current_price=current_price,
            price_history=prices,
            current_position=None,
            balance=balance
        )
        
        # Convert AI signal to boolean decision
        should_buy = decision.signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]
        
        return should_buy, decision.reasoning
    
    def should_sell(self, prices: pd.Series, entry_price: float, current_price: float, balance: float) -> tuple[bool, str]:
        """AI-enhanced sell decision"""
        
        # Create position info for AI
        current_position = {
            'price': entry_price,
            'amount': 0.1,  # This would be actual position size
            'timestamp': datetime.now()
        }
        
        # Get AI decision
        decision = self.ai_agent.make_trading_decision(
            current_price=current_price,
            price_history=prices,
            current_position=current_position,
            balance=balance
        )
        
        # Convert AI signal to boolean decision
        should_sell = decision.signal in [TradingSignal.SELL, TradingSignal.STRONG_SELL]
        
        return should_sell, decision.reasoning


class AIEnhancedFreqtradeBot(FreqtradeBotCore):
    """
    Freqtrade bot enhanced with AI agent capabilities.
    """
    
    def __init__(self, config: Dict):
        super().__init__(config)
        
        # Initialize AI agent
        ai_config = config.get('ai_config', {})
        self.ai_agent = AITradingAgent(ai_config)
        
        # Replace strategy with AI-enhanced version
        self.strategy = AIEnhancedTradingStrategy(
            ai_agent=self.ai_agent,
            short_window=config.get('short_window', 10),
            long_window=config.get('long_window', 30),
            rsi_period=config.get('rsi_period', 14)
        )
        
        logger.info("AI-Enhanced Freqtrade Bot initialized")
    
    async def trading_loop(self):
        """Enhanced trading loop with AI integration"""
        logger.info("Starting AI-enhanced trading loop...")
        
        while self.is_running:
            try:
                # Fetch current market data
                current_price = await self.fetch_market_data()
                if current_price is None:
                    await asyncio.sleep(10)
                    continue
                
                logger.info(f"Current price: {current_price}")
                
                # Check if we have enough data for AI analysis
                if len(self.price_history) < 30:  # Reduced requirement for AI
                    logger.info(f"Collecting data for AI analysis... {len(self.price_history)}/30")
                    await asyncio.sleep(10)
                    continue
                
                # Execute AI-enhanced trading logic
                if self.current_position is None:
                    # Look for buy signals using AI
                    should_buy, reasoning = self.strategy.should_buy(
                        self.price_history, current_price, self.balance
                    )
                    logger.info(f"AI Buy analysis: {reasoning}")
                    
                    if should_buy:
                        # Get AI-suggested position size
                        decision = self.ai_agent.last_decision
                        if decision and decision.suggested_position_size > 0:
                            amount = min(decision.suggested_position_size, self.balance * 0.2 / current_price)
                            
                            if self.execute_buy_order(current_price, amount):
                                logger.info(f"AI-guided position opened: {amount} at {current_price}")
                
                else:
                    # Look for sell signals using AI
                    entry_price = self.current_position['price']
                    should_sell, reasoning = self.strategy.should_sell(
                        self.price_history, entry_price, current_price, self.balance
                    )
                    logger.info(f"AI Sell analysis: {reasoning}")
                    
                    if should_sell:
                        amount = self.current_position['amount']
                        if self.execute_sell_order(current_price, amount):
                            logger.info(f"AI-guided position closed: {amount} at {current_price}")
                            
                            # Update AI performance
                            profit_loss = (current_price - entry_price) * amount
                            profit_pct = ((current_price - entry_price) / entry_price) * 100
                            
                            trade_result = {
                                'profit_loss': profit_loss,
                                'profit_pct': profit_pct,
                                'entry_price': entry_price,
                                'exit_price': current_price
                            }
                            
                            self.ai_agent.update_performance(trade_result)
                
                # Log enhanced status
                total_value = self.balance
                if self.current_position:
                    total_value += self.current_position['amount'] * current_price
                
                ai_status = self.ai_agent.get_agent_status()
                
                logger.info(f"Balance: {self.balance:.2f}, Total Value: {total_value:.2f}, Trades: {len(self.trade_history)}")
                logger.info(f"AI Status: Regime={ai_status['last_analysis']['regime']}, "
                           f"Signal={ai_status['last_decision']['signal']}, "
                           f"Confidence={ai_status['last_decision']['confidence']:.3f}")
                
                # Wait before next iteration
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in AI-enhanced trading loop: {e}")
                await asyncio.sleep(10)
    
    def get_status(self) -> Dict:
        """Get enhanced status including AI information"""
        base_status = super().get_status()
        
        # Add AI status
        ai_status = self.ai_agent.get_agent_status()
        base_status['ai_status'] = ai_status
        
        return base_status


async def main():
    """Main function for testing the AI-enhanced bot"""
    config = {
        'exchange': 'binance',
        'trading_pair': 'BTC/USDT',
        'timeframe': '1m',
        'initial_balance': 1000.0,
        'sandbox': True,
        'short_window': 10,
        'long_window': 30,
        'rsi_period': 14,
        'ai_config': {
            'learning_rate': 0.01,
            'exploration_rate': 0.1
        }
    }
    
    bot = AIEnhancedFreqtradeBot(config)
    bot.start()
    
    try:
        # Run for a limited time for testing
        await asyncio.wait_for(bot.trading_loop(), timeout=180)  # 3 minutes
    except asyncio.TimeoutError:
        logger.info("AI-enhanced test run completed")
    except KeyboardInterrupt:
        logger.info("AI-enhanced bot stopped by user")
    finally:
        bot.stop()
        
        # Print final status
        status = bot.get_status()
        print("\n=== Final AI-Enhanced Status ===")
        for key, value in status.items():
            if key != 'ai_status':
                print(f"{key}: {value}")
        
        print("\n=== AI Agent Status ===")
        ai_status = status.get('ai_status', {})
        for key, value in ai_status.items():
            print(f"{key}: {value}")


if __name__ == "__main__":
    asyncio.run(main())

