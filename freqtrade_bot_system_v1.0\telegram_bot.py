#!/usr/bin/env python3
"""
Telegram Bot Integration for Freqtrade Bot System
Provides user interface and control via Telegram messaging.
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional
import threading

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters

from ai_enhanced_bot import AIEnhancedFreqtradeBot

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BotManager:
    """
    Manages multiple Freqtrade bot instances.
    """
    
    def __init__(self):
        self.bots: Dict[str, AIEnhancedFreqtradeBot] = {}
        self.bot_configs: Dict[str, Dict] = {}
        self.bot_tasks: Dict[str, asyncio.Task] = {}
        
    def create_bot(self, bot_id: str, config: Dict) -> bool:
        """Create a new bot instance"""
        try:
            if bot_id in self.bots:
                logger.warning(f"Bot {bot_id} already exists")
                return False
            
            bot = AIEnhancedFreqtradeBot(config)
            self.bots[bot_id] = bot
            self.bot_configs[bot_id] = config.copy()
            
            logger.info(f"Bot {bot_id} created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create bot {bot_id}: {e}")
            return False
    
    async def start_bot(self, bot_id: str) -> bool:
        """Start a bot instance"""
        try:
            if bot_id not in self.bots:
                logger.error(f"Bot {bot_id} does not exist")
                return False
            
            if bot_id in self.bot_tasks and not self.bot_tasks[bot_id].done():
                logger.warning(f"Bot {bot_id} is already running")
                return False
            
            bot = self.bots[bot_id]
            bot.start()
            
            # Start the trading loop in a separate task
            self.bot_tasks[bot_id] = asyncio.create_task(bot.trading_loop())
            
            logger.info(f"Bot {bot_id} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start bot {bot_id}: {e}")
            return False
    
    async def stop_bot(self, bot_id: str) -> bool:
        """Stop a bot instance"""
        try:
            if bot_id not in self.bots:
                logger.error(f"Bot {bot_id} does not exist")
                return False
            
            bot = self.bots[bot_id]
            bot.stop()
            
            # Cancel the trading loop task
            if bot_id in self.bot_tasks and not self.bot_tasks[bot_id].done():
                self.bot_tasks[bot_id].cancel()
                try:
                    await self.bot_tasks[bot_id]
                except asyncio.CancelledError:
                    pass
            
            logger.info(f"Bot {bot_id} stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop bot {bot_id}: {e}")
            return False
    
    def get_bot_status(self, bot_id: str) -> Optional[Dict]:
        """Get status of a specific bot"""
        if bot_id not in self.bots:
            return None
        
        bot = self.bots[bot_id]
        status = bot.get_status()
        
        # Add task status
        if bot_id in self.bot_tasks:
            task = self.bot_tasks[bot_id]
            status['task_running'] = not task.done()
            if task.done():
                try:
                    exception = task.exception()
                    if exception and not isinstance(exception, asyncio.CancelledError):
                        status['task_error'] = str(exception)
                except asyncio.CancelledError:
                    # Task was cancelled, this is normal
                    pass
        else:
            status['task_running'] = False
        
        return status
    
    def list_bots(self) -> List[str]:
        """List all bot IDs"""
        return list(self.bots.keys())
    
    def delete_bot(self, bot_id: str) -> bool:
        """Delete a bot instance"""
        try:
            if bot_id not in self.bots:
                logger.error(f"Bot {bot_id} does not exist")
                return False
            
            # Stop the bot first
            asyncio.create_task(self.stop_bot(bot_id))
            
            # Remove from dictionaries
            del self.bots[bot_id]
            del self.bot_configs[bot_id]
            if bot_id in self.bot_tasks:
                del self.bot_tasks[bot_id]
            
            logger.info(f"Bot {bot_id} deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete bot {bot_id}: {e}")
            return False


class TelegramBotInterface:
    """
    Telegram bot interface for controlling Freqtrade bots.
    """
    
    def __init__(self, token: str):
        self.token = token
        self.bot_manager = BotManager()
        self.application = None
        self.authorized_users = set()  # Add user IDs here for security
        
        # Default bot configuration template
        self.default_config = {
            'exchange': 'binance',
            'trading_pair': 'BTC/USDT',
            'timeframe': '1m',
            'initial_balance': 1000.0,
            'sandbox': True,
            'short_window': 10,
            'long_window': 30,
            'rsi_period': 14,
            'ai_config': {
                'learning_rate': 0.01,
                'exploration_rate': 0.1
            }
        }
    
    def is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized (for security)"""
        # For demo purposes, allow all users
        # In production, implement proper authorization
        return True
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return
        
        welcome_message = """
🤖 **Freqtrade AI Bot System**

Welcome! This bot allows you to control multiple AI-enhanced trading bots.

**Available Commands:**
/help - Show this help message
/create_bot - Create a new trading bot
/list_bots - List all your bots
/start_bot - Start a trading bot
/stop_bot - Stop a trading bot
/status - Get bot status
/delete_bot - Delete a bot

**Quick Start:**
1. Use /create_bot to create your first bot
2. Use /start_bot to begin trading
3. Use /status to monitor performance

⚠️ **Important:** This is a demo system. Always test with small amounts first!
        """
        
        await update.message.reply_text(welcome_message, parse_mode='Markdown')
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        await self.start_command(update, context)
    
    async def create_bot_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /create_bot command"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return
        
        # Parse arguments
        args = context.args
        if len(args) < 1:
            await update.message.reply_text(
                "❌ Usage: /create_bot <bot_name> [trading_pair]\n"
                "Example: /create_bot my_btc_bot BTC/USDT"
            )
            return
        
        bot_id = args[0]
        trading_pair = args[1] if len(args) > 1 else 'BTC/USDT'
        
        # Create bot configuration
        config = self.default_config.copy()
        config['trading_pair'] = trading_pair
        
        # Create the bot
        if self.bot_manager.create_bot(bot_id, config):
            await update.message.reply_text(
                f"✅ Bot '{bot_id}' created successfully!\n"
                f"Trading pair: {trading_pair}\n"
                f"Use /start_bot {bot_id} to begin trading."
            )
        else:
            await update.message.reply_text(f"❌ Failed to create bot '{bot_id}'")
    
    async def list_bots_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /list_bots command"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return
        
        bots = self.bot_manager.list_bots()
        
        if not bots:
            await update.message.reply_text("📭 No bots created yet. Use /create_bot to create one.")
            return
        
        message = "🤖 **Your Trading Bots:**\n\n"
        
        for bot_id in bots:
            status = self.bot_manager.get_bot_status(bot_id)
            if status:
                running_status = "🟢 Running" if status.get('is_running', False) else "🔴 Stopped"
                task_status = "✅ Active" if status.get('task_running', False) else "⏸️ Idle"
                
                message += f"**{bot_id}**\n"
                message += f"  Status: {running_status}\n"
                message += f"  Task: {task_status}\n"
                message += f"  Pair: {status.get('trading_pair', 'N/A')}\n"
                message += f"  Balance: ${status.get('balance', 0):.2f}\n"
                message += f"  Total Value: ${status.get('total_value', 0):.2f}\n"
                message += f"  Trades: {status.get('total_trades', 0)}\n\n"
        
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def start_bot_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start_bot command"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return
        
        args = context.args
        if len(args) < 1:
            await update.message.reply_text("❌ Usage: /start_bot <bot_name>")
            return
        
        bot_id = args[0]
        
        if await self.bot_manager.start_bot(bot_id):
            await update.message.reply_text(f"✅ Bot '{bot_id}' started successfully! 🚀")
        else:
            await update.message.reply_text(f"❌ Failed to start bot '{bot_id}'")
    
    async def stop_bot_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stop_bot command"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return
        
        args = context.args
        if len(args) < 1:
            await update.message.reply_text("❌ Usage: /stop_bot <bot_name>")
            return
        
        bot_id = args[0]
        
        if await self.bot_manager.stop_bot(bot_id):
            await update.message.reply_text(f"✅ Bot '{bot_id}' stopped successfully! ⏹️")
        else:
            await update.message.reply_text(f"❌ Failed to stop bot '{bot_id}'")
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return
        
        args = context.args
        if len(args) < 1:
            # Show status of all bots
            bots = self.bot_manager.list_bots()
            if not bots:
                await update.message.reply_text("📭 No bots created yet.")
                return
            
            message = "📊 **Bot Status Summary:**\n\n"
            total_value = 0
            total_trades = 0
            
            for bot_id in bots:
                status = self.bot_manager.get_bot_status(bot_id)
                if status:
                    total_value += status.get('total_value', 0)
                    total_trades += status.get('total_trades', 0)
                    
                    running = "🟢" if status.get('is_running', False) else "🔴"
                    message += f"{running} **{bot_id}**: ${status.get('total_value', 0):.2f} ({status.get('total_trades', 0)} trades)\n"
            
            message += f"\n💰 **Total Portfolio Value:** ${total_value:.2f}\n"
            message += f"📈 **Total Trades:** {total_trades}\n"
            
            await update.message.reply_text(message, parse_mode='Markdown')
            return
        
        # Show detailed status of specific bot
        bot_id = args[0]
        status = self.bot_manager.get_bot_status(bot_id)
        
        if not status:
            await update.message.reply_text(f"❌ Bot '{bot_id}' not found")
            return
        
        # Format detailed status
        message = f"📊 **Detailed Status: {bot_id}**\n\n"
        
        # Basic info
        running = "🟢 Running" if status.get('is_running', False) else "🔴 Stopped"
        message += f"**Status:** {running}\n"
        message += f"**Trading Pair:** {status.get('trading_pair', 'N/A')}\n"
        message += f"**Current Price:** ${status.get('current_price', 0):.2f}\n\n"
        
        # Financial info
        message += f"**💰 Financial Status:**\n"
        message += f"Balance: ${status.get('balance', 0):.2f}\n"
        message += f"Total Value: ${status.get('total_value', 0):.2f}\n"
        message += f"P&L: ${status.get('profit_loss', 0):.2f} ({status.get('profit_pct', 0):.2f}%)\n"
        message += f"Total Trades: {status.get('total_trades', 0)}\n\n"
        
        # Position info
        position = status.get('current_position')
        if position:
            message += f"**📈 Current Position:**\n"
            message += f"Side: {position.get('side', 'N/A')}\n"
            message += f"Amount: {position.get('amount', 0):.6f}\n"
            message += f"Entry Price: ${position.get('price', 0):.2f}\n"
        else:
            message += f"**📈 Current Position:** None\n"
        
        # AI status
        ai_status = status.get('ai_status', {})
        if ai_status:
            message += f"\n**🤖 AI Agent Status:**\n"
            last_analysis = ai_status.get('last_analysis', {})
            last_decision = ai_status.get('last_decision', {})
            
            message += f"Market Regime: {last_analysis.get('regime', 'unknown')}\n"
            message += f"Last Signal: {last_decision.get('signal', 'none')}\n"
            message += f"Confidence: {last_decision.get('confidence', 0):.3f}\n"
            message += f"Total Decisions: {ai_status.get('total_decisions', 0)}\n"
        
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def delete_bot_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /delete_bot command"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Unauthorized access")
            return
        
        args = context.args
        if len(args) < 1:
            await update.message.reply_text("❌ Usage: /delete_bot <bot_name>")
            return
        
        bot_id = args[0]
        
        if self.bot_manager.delete_bot(bot_id):
            await update.message.reply_text(f"✅ Bot '{bot_id}' deleted successfully! 🗑️")
        else:
            await update.message.reply_text(f"❌ Failed to delete bot '{bot_id}'")
    
    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        logger.error(f"Exception while handling an update: {context.error}")
    
    def setup_handlers(self):
        """Setup command handlers"""
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("create_bot", self.create_bot_command))
        self.application.add_handler(CommandHandler("list_bots", self.list_bots_command))
        self.application.add_handler(CommandHandler("start_bot", self.start_bot_command))
        self.application.add_handler(CommandHandler("stop_bot", self.stop_bot_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("delete_bot", self.delete_bot_command))
        
        # Error handler
        self.application.add_error_handler(self.error_handler)
    
    async def run(self):
        """Run the Telegram bot"""
        # Create application
        self.application = Application.builder().token(self.token).build()
        
        # Setup handlers
        self.setup_handlers()
        
        # Start the bot
        logger.info("Starting Telegram bot...")
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
        logger.info("Telegram bot is running!")
        
        # Keep running
        try:
            await asyncio.Event().wait()
        except KeyboardInterrupt:
            logger.info("Stopping Telegram bot...")
        finally:
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()


async def main():
    """Main function for testing"""
    # For testing, use a dummy token
    # In production, get this from environment variable or config file
    token = os.getenv('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')
    
    if token == 'YOUR_BOT_TOKEN_HERE':
        print("⚠️  Please set TELEGRAM_BOT_TOKEN environment variable")
        print("To get a token:")
        print("1. Message @BotFather on Telegram")
        print("2. Use /newbot command")
        print("3. Follow the instructions")
        print("4. Set the token: export TELEGRAM_BOT_TOKEN='your_token_here'")
        return
    
    # Create and run the Telegram bot
    telegram_bot = TelegramBotInterface(token)
    await telegram_bot.run()


if __name__ == "__main__":
    asyncio.run(main())

