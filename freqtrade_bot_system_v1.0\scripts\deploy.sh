#!/bin/bash
# Freqtrade Bot System - Deployment Script
# This script automates the deployment of the complete Freqtrade Bot System

set -e  # Exit on any error

echo "=== Freqtrade Bot System Deployment Script ==="
echo "This script will install and configure the complete trading bot system."
echo ""

# Configuration
INSTALL_DIR="$HOME/freqtrade_bot_system"
CONFIG_DIR="$HOME/.freqtrade_bot_system"
VENV_DIR="$HOME/freqtrade_env"
LOG_FILE="/tmp/freqtrade_deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    log "ERROR: $1"
    exit 1
}

# Success message
success() {
    echo -e "${GREEN}✓ $1${NC}"
    log "SUCCESS: $1"
}

# Warning message
warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    log "WARNING: $1"
}

# Info message
info() {
    echo -e "${BLUE}ℹ $1${NC}"
    log "INFO: $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error_exit "This script should not be run as root. Please run as a regular user."
    fi
}

# Check system requirements
check_requirements() {
    info "Checking system requirements..."
    
    # Check OS
    if [[ ! -f /etc/os-release ]]; then
        error_exit "Cannot determine operating system"
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" ]] && [[ "$ID_LIKE" != *"ubuntu"* ]] && [[ "$ID_LIKE" != *"debian"* ]]; then
        warning "This script is designed for Ubuntu/Debian. Your OS: $PRETTY_NAME"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    # Check Python version
    if ! command -v python3.11 &> /dev/null; then
        if ! command -v python3 &> /dev/null; then
            error_exit "Python 3 is not installed"
        fi
        
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [[ $(echo "$PYTHON_VERSION < 3.9" | bc -l) -eq 1 ]]; then
            error_exit "Python 3.9+ is required. Found: $PYTHON_VERSION"
        fi
        
        warning "Python 3.11 not found, using python3 (version $PYTHON_VERSION)"
        PYTHON_CMD="python3"
    else
        PYTHON_CMD="python3.11"
        success "Python 3.11 found"
    fi
    
    # Check available disk space (minimum 2GB)
    AVAILABLE_SPACE=$(df "$HOME" | awk 'NR==2 {print $4}')
    if [[ $AVAILABLE_SPACE -lt 2097152 ]]; then  # 2GB in KB
        error_exit "Insufficient disk space. At least 2GB required."
    fi
    
    # Check memory (minimum 4GB)
    TOTAL_MEM=$(free -m | awk 'NR==2{print $2}')
    if [[ $TOTAL_MEM -lt 4096 ]]; then
        warning "Less than 4GB RAM detected. System may not perform optimally with many bot instances."
    fi
    
    success "System requirements check completed"
}

# Install system dependencies
install_system_deps() {
    info "Installing system dependencies..."
    
    # Update package list
    sudo apt update || error_exit "Failed to update package list"
    
    # Install required packages
    PACKAGES=(
        "build-essential"
        "libta-lib-dev"
        "python3-pip"
        "python3-venv"
        "git"
        "curl"
        "wget"
        "unzip"
        "bc"
    )
    
    for package in "${PACKAGES[@]}"; do
        if ! dpkg -l | grep -q "^ii  $package "; then
            info "Installing $package..."
            sudo apt install -y "$package" || error_exit "Failed to install $package"
        else
            success "$package already installed"
        fi
    done
    
    # Install TA-Lib from source if not available
    if ! ldconfig -p | grep -q "libta_lib"; then
        info "Installing TA-Lib from source..."
        cd /tmp
        wget -q http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
        tar -xzf ta-lib-0.4.0-src.tar.gz
        cd ta-lib/
        ./configure --prefix=/usr
        make && sudo make install
        sudo ldconfig
        cd "$HOME"
        success "TA-Lib installed successfully"
    else
        success "TA-Lib already installed"
    fi
}

# Create Python virtual environment
setup_python_env() {
    info "Setting up Python virtual environment..."
    
    if [[ -d "$VENV_DIR" ]]; then
        warning "Virtual environment already exists at $VENV_DIR"
        read -p "Remove and recreate? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rm -rf "$VENV_DIR"
        else
            info "Using existing virtual environment"
            return 0
        fi
    fi
    
    # Create virtual environment
    $PYTHON_CMD -m venv "$VENV_DIR" || error_exit "Failed to create virtual environment"
    
    # Activate virtual environment
    source "$VENV_DIR/bin/activate" || error_exit "Failed to activate virtual environment"
    
    # Upgrade pip
    pip install --upgrade pip setuptools wheel || error_exit "Failed to upgrade pip"
    
    success "Python virtual environment created"
}

# Install Python dependencies
install_python_deps() {
    info "Installing Python dependencies..."
    
    # Activate virtual environment
    source "$VENV_DIR/bin/activate" || error_exit "Failed to activate virtual environment"
    
    # Install core dependencies
    PYTHON_PACKAGES=(
        "ccxt"
        "pandas"
        "numpy<2.0"
        "python-telegram-bot"
        "aiohttp"
        "fastapi"
        "uvicorn"
        "grpcio"
        "grpcio-tools"
        "psutil"
        "asyncio"
    )
    
    for package in "${PYTHON_PACKAGES[@]}"; do
        info "Installing $package..."
        pip install "$package" || error_exit "Failed to install $package"
    done
    
    # Try to install freqtrade (may fail due to dependencies)
    info "Installing Freqtrade..."
    pip install freqtrade --no-deps || warning "Freqtrade installation failed, continuing with core functionality"
    
    success "Python dependencies installed"
}

# Create directory structure
create_directories() {
    info "Creating directory structure..."
    
    # Create main directories
    mkdir -p "$INSTALL_DIR" || error_exit "Failed to create install directory"
    mkdir -p "$CONFIG_DIR"/{config,logs,data,backups} || error_exit "Failed to create config directories"
    mkdir -p /tmp/freqtrade_bots || error_exit "Failed to create temp directory"
    
    success "Directory structure created"
}

# Copy system files
copy_files() {
    info "Copying system files..."
    
    # Check if we're in the source directory
    if [[ ! -f "master_control.py" ]]; then
        error_exit "Source files not found. Please run this script from the freqtrade_bot_system directory."
    fi
    
    # Copy Python modules
    PYTHON_FILES=(
        "freqtrade_bot_core.py"
        "ai_agent.py"
        "ai_enhanced_bot.py"
        "telegram_bot.py"
        "bisq_integration.py"
        "parallel_execution.py"
        "master_control.py"
        "test_suite.py"
    )
    
    for file in "${PYTHON_FILES[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$INSTALL_DIR/" || error_exit "Failed to copy $file"
            success "Copied $file"
        else
            warning "$file not found, skipping"
        fi
    done
    
    # Copy configuration files
    if [[ -f "config.json" ]]; then
        cp "config.json" "$CONFIG_DIR/config/master_config.json" || error_exit "Failed to copy config.json"
        success "Copied configuration file"
    fi
    
    # Copy documentation
    if [[ -f "documentation.md" ]]; then
        cp "documentation.md" "$INSTALL_DIR/" || error_exit "Failed to copy documentation"
        success "Copied documentation"
    fi
    
    # Make scripts executable
    chmod +x "$INSTALL_DIR"/*.py
}

# Create configuration files
create_configs() {
    info "Creating configuration files..."
    
    # Create master configuration
    cat > "$CONFIG_DIR/config/master_config.json" << EOF
{
  "system": {
    "max_bot_instances": 100,
    "log_level": "INFO",
    "data_directory": "$CONFIG_DIR/data",
    "log_directory": "$CONFIG_DIR/logs"
  },
  "telegram": {
    "enabled": false,
    "bot_token": "",
    "allowed_users": [],
    "command_prefix": "/"
  },
  "bisq": {
    "enabled": false,
    "host": "localhost",
    "port": 9998,
    "password": "",
    "auto_confirm_payments": false
  },
  "default_trading_config": {
    "exchange": "binance",
    "timeframe": "1m",
    "initial_balance": 1000.0,
    "sandbox": true,
    "max_open_trades": 3,
    "stake_amount": 100.0,
    "dry_run": true
  },
  "ai_agent": {
    "learning_rate": 0.01,
    "exploration_rate": 0.1,
    "model_update_frequency": 100,
    "confidence_threshold": 0.7
  },
  "risk_management": {
    "max_drawdown": 0.2,
    "stop_loss": 0.05,
    "take_profit": 0.1,
    "position_sizing": "fixed"
  }
}
EOF
    
    # Create environment file template
    cat > "$CONFIG_DIR/config/environment.env" << EOF
# Freqtrade Bot System Environment Variables
# Copy this file to .env and fill in your actual values

# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_USER_ID=your_telegram_user_id_here

# Exchange API Keys
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

COINBASE_API_KEY=your_coinbase_api_key_here
COINBASE_API_SECRET=your_coinbase_api_secret_here
COINBASE_PASSPHRASE=your_coinbase_passphrase_here

# Bisq Configuration
BISQ_API_PASSWORD=your_bisq_api_password_here

# System Configuration
FREQTRADE_LOG_LEVEL=INFO
FREQTRADE_DATA_DIR=$CONFIG_DIR/data
FREQTRADE_LOG_DIR=$CONFIG_DIR/logs
EOF
    
    success "Configuration files created"
}

# Create startup scripts
create_startup_scripts() {
    info "Creating startup scripts..."
    
    # Create main startup script
    cat > "$INSTALL_DIR/start_system.sh" << EOF
#!/bin/bash
# Freqtrade Bot System Startup Script

# Activate virtual environment
source "$VENV_DIR/bin/activate"

# Load environment variables
if [[ -f "$CONFIG_DIR/config/.env" ]]; then
    source "$CONFIG_DIR/config/.env"
fi

# Change to install directory
cd "$INSTALL_DIR"

# Start the system
echo "Starting Freqtrade Bot System..."
python master_control.py --config "$CONFIG_DIR/config/master_config.json" "\$@"
EOF
    
    # Create stop script
    cat > "$INSTALL_DIR/stop_system.sh" << EOF
#!/bin/bash
# Freqtrade Bot System Stop Script

echo "Stopping Freqtrade Bot System..."
pkill -f "master_control.py"
pkill -f "bot_"
echo "System stopped."
EOF
    
    # Create status script
    cat > "$INSTALL_DIR/status.sh" << EOF
#!/bin/bash
# Freqtrade Bot System Status Script

echo "=== Freqtrade Bot System Status ==="
echo ""

# Check if main process is running
if pgrep -f "master_control.py" > /dev/null; then
    echo "✓ Master Control System: RUNNING"
    echo "  PID: \$(pgrep -f master_control.py)"
else
    echo "✗ Master Control System: STOPPED"
fi

# Check bot processes
BOT_COUNT=\$(pgrep -f "bot_" | wc -l)
echo "✓ Active Bot Instances: \$BOT_COUNT"

# Check system resources
echo ""
echo "=== System Resources ==="
echo "CPU Usage: \$(top -bn1 | grep "Cpu(s)" | awk '{print \$2}' | cut -d'%' -f1)%"
echo "Memory Usage: \$(free | grep Mem | awk '{printf("%.1f%%", \$3/\$2 * 100.0)}')"
echo "Disk Usage: \$(df -h \$HOME | awk 'NR==2 {print \$5}')"

# Check log files
echo ""
echo "=== Recent Logs ==="
if [[ -f "$CONFIG_DIR/logs/master.log" ]]; then
    echo "Last 5 log entries:"
    tail -5 "$CONFIG_DIR/logs/master.log"
else
    echo "No log files found"
fi
EOF
    
    # Make scripts executable
    chmod +x "$INSTALL_DIR"/*.sh
    
    success "Startup scripts created"
}

# Create systemd service (optional)
create_systemd_service() {
    info "Creating systemd service..."
    
    read -p "Create systemd service for auto-start? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        info "Skipping systemd service creation"
        return 0
    fi
    
    # Create service file
    cat > /tmp/freqtrade-bot-system.service << EOF
[Unit]
Description=Freqtrade Bot System
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$INSTALL_DIR
ExecStart=$INSTALL_DIR/start_system.sh
ExecStop=$INSTALL_DIR/stop_system.sh
Restart=always
RestartSec=10
Environment=HOME=$HOME

[Install]
WantedBy=multi-user.target
EOF
    
    # Install service
    sudo mv /tmp/freqtrade-bot-system.service /etc/systemd/system/
    sudo systemctl daemon-reload
    sudo systemctl enable freqtrade-bot-system.service
    
    success "Systemd service created and enabled"
    info "Use 'sudo systemctl start freqtrade-bot-system' to start the service"
}

# Run tests
run_tests() {
    info "Running system tests..."
    
    # Activate virtual environment
    source "$VENV_DIR/bin/activate"
    
    # Change to install directory
    cd "$INSTALL_DIR"
    
    # Run basic import tests
    python -c "
import sys
try:
    import freqtrade_bot_core
    import ai_agent
    import telegram_bot
    import bisq_integration
    import parallel_execution
    import master_control
    print('✓ All modules imported successfully')
except ImportError as e:
    print(f'✗ Import error: {e}')
    sys.exit(1)
" || error_exit "Module import tests failed"
    
    success "Basic tests passed"
}

# Create README
create_readme() {
    info "Creating README file..."
    
    cat > "$INSTALL_DIR/README.md" << EOF
# Freqtrade Bot System

## Quick Start

### 1. Configuration
Edit the configuration files in \`$CONFIG_DIR/config/\`:
- \`master_config.json\` - Main system configuration
- \`.env\` - Environment variables (copy from environment.env template)

### 2. Start the System
\`\`\`bash
cd $INSTALL_DIR
./start_system.sh
\`\`\`

### 3. Check Status
\`\`\`bash
./status.sh
\`\`\`

### 4. Stop the System
\`\`\`bash
./stop_system.sh
\`\`\`

## Configuration

### Telegram Bot Setup
1. Create a bot with @BotFather on Telegram
2. Get your bot token
3. Add the token to \`.env\` file
4. Enable Telegram in \`master_config.json\`

### Exchange API Setup
1. Create API keys on your preferred exchange
2. Add keys to \`.env\` file
3. Configure exchange settings in \`master_config.json\`

### Bisq Integration (Optional)
1. Install and configure Bisq application
2. Enable API access in Bisq
3. Configure Bisq settings in \`master_config.json\`

## Usage

### Creating Bots
\`\`\`python
from master_control import MasterControlSystem, SystemConfig

config = SystemConfig()
master = MasterControlSystem(config)
await master.initialize()

# Create a new bot
bot_id = master.create_bot_with_config("BTC_Trader", {
    "trading_pair": "BTC/USDT",
    "initial_balance": 1000.0
})

# Start the bot
success, message = master.start_bot(bot_id)
\`\`\`

### Telegram Commands
- \`/status\` - System overview
- \`/create <name> <pair>\` - Create new bot
- \`/start <bot_id>\` - Start bot
- \`/stop <bot_id>\` - Stop bot
- \`/list\` - List all bots

## Documentation

See \`documentation.md\` for complete documentation.

## Support

For issues and questions, check the logs in \`$CONFIG_DIR/logs/\`.
EOF
    
    success "README created"
}

# Main deployment function
main() {
    echo "Starting deployment at $(date)"
    log "=== Freqtrade Bot System Deployment Started ==="
    
    check_root
    check_requirements
    install_system_deps
    setup_python_env
    install_python_deps
    create_directories
    copy_files
    create_configs
    create_startup_scripts
    create_systemd_service
    run_tests
    create_readme
    
    echo ""
    echo "=== Deployment Complete ==="
    success "Freqtrade Bot System has been successfully deployed!"
    echo ""
    echo "Installation Directory: $INSTALL_DIR"
    echo "Configuration Directory: $CONFIG_DIR"
    echo "Virtual Environment: $VENV_DIR"
    echo ""
    echo "Next Steps:"
    echo "1. Edit configuration files in $CONFIG_DIR/config/"
    echo "2. Copy environment.env to .env and fill in your API keys"
    echo "3. Run: cd $INSTALL_DIR && ./start_system.sh"
    echo ""
    echo "For detailed instructions, see README.md and documentation.md"
    
    log "=== Deployment Completed Successfully ==="
}

# Run main function
main "$@"

