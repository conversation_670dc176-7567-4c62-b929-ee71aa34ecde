#!/usr/bin/env python3
"""
Master Control System
Integrates all components: Freqtrade bots, AI agents, Telegram interface, Bisq integration, and parallel execution.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

# Import our modules
from telegram_bot import BotManager
from parallel_execution import ParallelExecutionSystem
from bisq_integration import BisqIntegrationModule

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class SystemConfig:
    """Master system configuration"""
    max_bot_instances: int = 100
    telegram_token: str = ""
    bisq_enabled: bool = False
    bisq_host: str = "localhost"
    bisq_port: int = 9998
    bisq_password: str = ""
    default_trading_config: Dict[str, Any] = None


class MasterControlSystem:
    """
    Master control system that orchestrates all components.
    """
    
    def __init__(self, config: SystemConfig):
        self.config = config
        
        # Core systems
        self.parallel_system = ParallelExecutionSystem(config.max_bot_instances)
        self.telegram_manager = BotManager()
        self.bisq_integration = None
        
        if config.bisq_enabled:
            bisq_config = {
                'bisq_host': config.bisq_host,
                'bisq_port': config.bisq_port,
                'bisq_password': config.bisq_password
            }
            self.bisq_integration = BisqIntegrationModule(bisq_config)
        
        # State
        self.is_running = False
        self.start_time = None
        
        logger.info("Master Control System initialized")
    
    async def initialize(self) -> bool:
        """Initialize all systems"""
        try:
            logger.info("Initializing Master Control System...")
            
            # Start parallel execution monitoring
            await self.parallel_system.start_monitoring()
            
            # Initialize Bisq if enabled
            if self.bisq_integration:
                success = await self.bisq_integration.initialize()
                if not success:
                    logger.warning("Bisq integration failed to initialize")
            
            self.is_running = True
            self.start_time = asyncio.get_event_loop().time()
            
            logger.info("Master Control System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Master Control System: {e}")
            return False
    
    async def shutdown(self):
        """Shutdown all systems"""
        logger.info("Shutting down Master Control System...")
        
        self.is_running = False
        
        # Shutdown parallel system
        await self.parallel_system.shutdown()
        
        # Shutdown Bisq integration
        if self.bisq_integration:
            await self.bisq_integration.shutdown()
        
        logger.info("Master Control System shutdown complete")
    
    def create_bot_with_config(self, name: str, custom_config: Dict[str, Any] = None) -> str:
        """Create a new bot with configuration"""
        # Merge with default config
        config = self.config.default_trading_config.copy() if self.config.default_trading_config else {}
        if custom_config:
            config.update(custom_config)
        
        # Add Bisq integration if enabled
        if self.bisq_integration:
            config['use_bisq'] = True
            config['bisq_config'] = {
                'bisq_host': self.config.bisq_host,
                'bisq_port': self.config.bisq_port,
                'bisq_password': self.config.bisq_password
            }
        
        # Create bot instance
        bot_id = self.parallel_system.create_bot_instance(name, config)
        
        # Register with Telegram manager
        self.telegram_manager.bots[bot_id] = None  # Placeholder for now
        
        logger.info(f"Created bot {bot_id} with integrated configuration")
        return bot_id
    
    def start_bot(self, bot_id: str) -> tuple[bool, str]:
        """Start a bot instance"""
        return self.parallel_system.start_bot(bot_id)
    
    def stop_bot(self, bot_id: str) -> tuple[bool, str]:
        """Stop a bot instance"""
        return self.parallel_system.stop_bot(bot_id)
    
    def remove_bot(self, bot_id: str) -> tuple[bool, str]:
        """Remove a bot instance"""
        # Remove from Telegram manager
        if bot_id in self.telegram_manager.bots:
            del self.telegram_manager.bots[bot_id]
        
        return self.parallel_system.remove_bot(bot_id)
    
    def get_bot_status(self, bot_id: str) -> Optional[Dict]:
        """Get comprehensive bot status"""
        status = self.parallel_system.get_bot_status(bot_id)
        
        if status and self.bisq_integration:
            # Add Bisq-specific status
            bisq_status = self.bisq_integration.get_status()
            status['bisq_integration'] = bisq_status
        
        return status
    
    def list_all_bots(self) -> List[Dict]:
        """List all bots with comprehensive status"""
        return self.parallel_system.list_bots()
    
    def get_system_overview(self) -> Dict:
        """Get complete system overview"""
        parallel_status = self.parallel_system.get_system_status()
        
        overview = {
            'master_system': {
                'running': self.is_running,
                'uptime': asyncio.get_event_loop().time() - self.start_time if self.start_time else 0,
                'bisq_enabled': self.bisq_integration is not None
            },
            'parallel_execution': parallel_status,
            'telegram_manager': {
                'registered_bots': len(self.telegram_manager.bots),
                'active_tasks': len(self.telegram_manager.bot_tasks)
            }
        }
        
        if self.bisq_integration:
            overview['bisq_integration'] = self.bisq_integration.get_status()
        
        return overview
    
    async def handle_telegram_command(self, command: str, args: List[str]) -> str:
        """Handle Telegram commands through the master system"""
        try:
            if command == "status":
                overview = self.get_system_overview()
                return f"System Overview:\n{self._format_overview(overview)}"
            
            elif command == "create":
                if len(args) < 1:
                    return "Usage: /create <bot_name> [trading_pair]"
                
                name = args[0]
                config = {}
                if len(args) > 1:
                    config['trading_pair'] = args[1]
                
                bot_id = self.create_bot_with_config(name, config)
                return f"Created bot {bot_id} ({name})"
            
            elif command == "start":
                if len(args) < 1:
                    return "Usage: /start <bot_id>"
                
                bot_id = args[0]
                success, message = self.start_bot(bot_id)
                return f"Start bot {bot_id}: {'✓' if success else '✗'} {message}"
            
            elif command == "stop":
                if len(args) < 1:
                    return "Usage: /stop <bot_id>"
                
                bot_id = args[0]
                success, message = self.stop_bot(bot_id)
                return f"Stop bot {bot_id}: {'✓' if success else '✗'} {message}"
            
            elif command == "list":
                bots = self.list_all_bots()
                if not bots:
                    return "No bots found"
                
                response = "Bot List:\n"
                for bot in bots:
                    response += f"• {bot['id']} ({bot['name']}) - {bot['status']}\n"
                return response
            
            elif command == "remove":
                if len(args) < 1:
                    return "Usage: /remove <bot_id>"
                
                bot_id = args[0]
                success, message = self.remove_bot(bot_id)
                return f"Remove bot {bot_id}: {'✓' if success else '✗'} {message}"
            
            else:
                return f"Unknown command: {command}"
                
        except Exception as e:
            logger.error(f"Error handling Telegram command {command}: {e}")
            return f"Error: {str(e)}"
    
    def _format_overview(self, overview: Dict) -> str:
        """Format system overview for display"""
        lines = []
        
        # Master system
        master = overview['master_system']
        lines.append(f"Master: {'Running' if master['running'] else 'Stopped'}")
        lines.append(f"Uptime: {master['uptime']:.1f}s")
        
        # Parallel execution
        parallel = overview['parallel_execution']
        lines.append(f"Bots: {parallel['running_instances']}/{parallel['total_instances']} running")
        lines.append(f"System CPU: {parallel['system_cpu_percent']:.1f}%")
        lines.append(f"System Memory: {parallel['system_memory_percent']:.1f}%")
        
        # Bisq integration
        if 'bisq_integration' in overview:
            bisq = overview['bisq_integration']
            lines.append(f"Bisq: {'Connected' if bisq['connected'] else 'Disconnected'}")
            lines.append(f"Bisq Orders: {bisq['active_offers']}")
        
        return "\n".join(lines)


# Demo and testing functions
async def demo_master_system():
    """Demo the complete master control system"""
    
    print("=== Master Control System Demo ===")
    
    # Configuration
    config = SystemConfig(
        max_bot_instances=10,
        telegram_token="demo_token",
        bisq_enabled=True,
        default_trading_config={
            'exchange': 'binance',
            'timeframe': '1m',
            'initial_balance': 1000.0,
            'sandbox': True
        }
    )
    
    # Create master system
    master = MasterControlSystem(config)
    
    print("\n1. Initializing master system...")
    success = await master.initialize()
    print(f"Initialization: {'Success' if success else 'Failed'}")
    
    if not success:
        return
    
    print("\n2. System overview:")
    overview = master.get_system_overview()
    print(master._format_overview(overview))
    
    print("\n3. Creating bots...")
    bot_configs = [
        ("BTC_Trader", {"trading_pair": "BTC/USDT"}),
        ("ETH_Trader", {"trading_pair": "ETH/USDT"}),
        ("Multi_Pair", {"trading_pair": "BTC/EUR"})
    ]
    
    bot_ids = []
    for name, config in bot_configs:
        bot_id = master.create_bot_with_config(name, config)
        bot_ids.append(bot_id)
        print(f"  Created: {bot_id} ({name})")
    
    print("\n4. Starting bots...")
    for bot_id in bot_ids:
        success, message = master.start_bot(bot_id)
        print(f"  {bot_id}: {'✓' if success else '✗'} {message}")
    
    print("\n5. Waiting for bots to initialize...")
    await asyncio.sleep(10)
    
    print("\n6. Bot statuses:")
    for bot_id in bot_ids:
        status = master.get_bot_status(bot_id)
        if status:
            print(f"  {bot_id} ({status['name']}):")
            print(f"    Status: {status['status']}")
            print(f"    CPU: {status['cpu_usage']:.1f}%")
            print(f"    Memory: {status['memory_usage']:.1f}MB")
    
    print("\n7. Testing Telegram commands...")
    commands = [
        ("status", []),
        ("list", []),
        ("stop", [bot_ids[0]]),
        ("start", [bot_ids[0]])
    ]
    
    for command, args in commands:
        response = await master.handle_telegram_command(command, args)
        print(f"  /{command} {' '.join(args)}: {response}")
    
    print("\n8. Final system overview:")
    overview = master.get_system_overview()
    print(master._format_overview(overview))
    
    print("\n9. Shutting down...")
    await master.shutdown()
    
    print("\n=== Demo Complete ===")


if __name__ == "__main__":
    asyncio.run(demo_master_system())

