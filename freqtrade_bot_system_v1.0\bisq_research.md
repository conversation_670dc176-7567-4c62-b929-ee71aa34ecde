# Bisq Exchange Integration Research

## Overview

Bisq is a decentralized peer-to-peer bitcoin exchange network that allows users to trade bitcoin for national currencies and other cryptocurrencies without relying on a central authority. Unlike centralized exchanges, Bisq operates as a desktop application that connects to a P2P network.

## Key Findings

### Bisq gRPC API

Bisq provides a gRPC-based API that was introduced in version 1.9.1. This API allows programmatic access to a subset of Bisq's functionality including:

- Check balances (BTC and BSQ)
- Transfer BTC and BSQ
- Create payment accounts
- View offers in the order book
- Create and take offers
- Execute trades
- Manage wallets

### API Architecture

The Bisq API is based on the gRPC framework and supports multiple language bindings including:
- Java
- Python
- CLI (bisq-cli)

### Important Limitations and Warnings

1. **Exclusive Access**: Never run an API Daemon and the Bisq desktop application on the same host simultaneously. They share the same default wallet and connection ports, which can lead to wallet corruption.

2. **Wallet Safety**: The API daemon and GUI share the same wallet. Users must ensure proper backup procedures and never run both simultaneously.

3. **Development vs Production**: The API includes development-only features that only work in regtest mode.

### Available Services

The Bisq gRPC API provides the following services:

1. **DisputeAgents** - Development only, regtest mode
2. **GetVersion** - Get current Bisq version
3. **Help** - CLI command documentation
4. **Offers** - Create, edit, list, and cancel offers
5. **PaymentAccounts** - Manage payment methods
6. **Price** - Get market prices
7. **ShutdownServer** - Shutdown the daemon
8. **Trades** - Execute and manage trades
9. **Wallets** - Wallet operations

### Authentication

All API requests require authentication using an API password that is set when starting the daemon with the `--apiPassword=<api-password>` option.

### Trading Capabilities

The API supports:
- Creating buy/sell offers
- Taking existing offers
- BSQ swap offers
- Managing trade execution
- Viewing trade history
- Canceling offers

## Technical Implementation Considerations

### Connection Requirements

- Bisq daemon must be running locally
- API daemon listens on configurable ports (default examples show ports 9998, 9999)
- gRPC communication over local network

### Development Environment

Bisq provides a test harness for development that includes:
- Local regtest bitcoin-core daemon
- Bisq seed node
- Arbitration node
- Two regtest API daemons (Alice on port 9998, Bob on port 9999)
- Pre-funded wallets (10 BTC each, plus BSQ)

### Integration Challenges

1. **P2P Nature**: Unlike centralized exchanges with instant order matching, Bisq requires peer-to-peer negotiation
2. **Security Deposits**: Trades require security deposits from both parties
3. **Payment Methods**: Need to set up and manage various payment accounts
4. **Trade Completion**: Trades may require human intervention for payment confirmation
5. **Liquidity**: Limited compared to centralized exchanges

## URLs and Resources

- Bisq gRPC API Documentation: https://bisq-network.github.io/slate/
- Bisq GitHub Repository: https://github.com/bisq-network/bisq
- Bisq Documentation: https://docs.bisq.network/
- Bisq Network Website: https://bisq.network/



## Detailed API Analysis

### Service Offers

The Offers service provides comprehensive functionality for managing trading offers:

**Available Methods:**
- `CancelOffer` - Cancel an open offer and remove it from the offer book
- `CreateBsqSwapOffer` - Create BSQ swap offers for trading BSQ tokens
- `CreateOffer` - Create standard v1 protocol offers
- `EditOffer` - Modify existing offers
- `GetBsqSwapOffer` - Retrieve specific BSQ swap offer details
- `GetBsqSwapOffers` - List all BSQ swap offers
- `GetMyBsqSwapOffer` - Get user's specific BSQ swap offer
- `GetMyBsqSwapOffers` - List user's BSQ swap offers
- `GetMyOffer` - Get user's specific offer
- `GetMyOffers` - List all user's offers
- `GetOffer` - Get specific offer details
- `GetOfferCategory` - Determine offer category
- `GetOffers` - List all available offers in the order book

### Service Trades

The Trades service handles trade execution and management:

**Available Methods:**
- `CloseTrade` - Close a completed trade and move it to trade history
- `ConfirmPaymentReceived` - Confirm that payment has been received (for BTC buyer)
- `ConfirmPaymentStarted` - Confirm that payment has been initiated
- `FailTrade` - Mark a trade as failed
- `GetTrade` - Get specific trade details
- `GetTrades` - List all trades
- `TakeOffer` - Take an existing offer to initiate a trade
- `UnFailTrade` - Reverse a failed trade status
- `WithdrawFunds` - Withdraw funds from completed trades

### Trade Flow Process

Based on the API methods, the typical Bisq trade flow involves:

1. **Offer Creation**: Use `CreateOffer` to create a buy/sell offer
2. **Offer Taking**: Another user uses `TakeOffer` to accept the offer
3. **Payment Process**: 
   - Seller uses `ConfirmPaymentStarted` when payment is initiated
   - Buyer uses `ConfirmPaymentReceived` when payment is confirmed
4. **Trade Completion**: Use `CloseTrade` to finalize the trade
5. **Fund Withdrawal**: Use `WithdrawFunds` to withdraw the traded funds

### Integration Challenges for Automated Trading

1. **Human Interaction Required**: Payment confirmation steps typically require human verification
2. **P2P Coordination**: Unlike centralized exchanges, trades require coordination between two parties
3. **Payment Method Complexity**: Different payment methods have different confirmation requirements
4. **Security Deposits**: Both parties must lock up security deposits during trades
5. **Dispute Resolution**: Failed trades may require arbitration

### CLI Examples

The API documentation shows CLI examples like:
```bash
./bisq-cli --password=xyz --port=9998 canceloffer --offer-id=83e8b2e2-51b6-4f39-a748-3ebd29c22aea
./bisq-cli --password=xyz --port=9998 createoffer --swap=true --direction=BUY --currency-code=BSQ --amount=0.50 --min-amount=0.25 --fixed-price=0.00005
./bisq-cli --password=xyz --port=9998 closetrade --trade-id=83e8b2e2
./bisq-cli --password=xyz --port=9998 confirmpaymentreceived --trade-id=83e8b2e2-51b6-4f39-a748-3ebd29c22aea
```

This indicates that the API is functional and provides the necessary endpoints for programmatic trading, though with the limitations mentioned above.

