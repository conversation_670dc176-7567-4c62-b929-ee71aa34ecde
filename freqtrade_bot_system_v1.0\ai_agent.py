#!/usr/bin/env python3
"""
AI Agent for Freqtrade Bot System
Implements intelligent decision-making and strategy optimization based on market conditions.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MarketRegime(Enum):
    """Market regime classification"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    UNKNOWN = "unknown"


class TradingSignal(Enum):
    """Trading signal types"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"


@dataclass
class MarketAnalysis:
    """Market analysis result"""
    regime: MarketRegime
    volatility: float
    trend_strength: float
    support_level: float
    resistance_level: float
    rsi: float
    momentum: float
    confidence: float


@dataclass
class TradingDecision:
    """AI trading decision"""
    signal: TradingSignal
    confidence: float
    reasoning: str
    suggested_position_size: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    strategy_params: Dict[str, Any]


class MarketAnalyzer:
    """
    Advanced market analysis module using multiple indicators and techniques.
    """
    
    def __init__(self):
        self.lookback_period = 100
        
    def calculate_volatility(self, prices: pd.Series) -> float:
        """Calculate price volatility using standard deviation of returns"""
        if len(prices) < 2:
            return 0.0
        
        returns = prices.pct_change().dropna()
        return returns.std() * np.sqrt(252)  # Annualized volatility
    
    def calculate_trend_strength(self, prices: pd.Series) -> float:
        """Calculate trend strength using linear regression slope"""
        if len(prices) < 10:
            return 0.0
        
        x = np.arange(len(prices))
        y = prices.values
        
        # Linear regression
        slope, _ = np.polyfit(x, y, 1)
        
        # Normalize slope relative to price level
        normalized_slope = slope / prices.mean()
        
        # Return value between -1 (strong downtrend) and 1 (strong uptrend)
        return np.tanh(normalized_slope * 1000)
    
    def calculate_support_resistance(self, prices: pd.Series) -> Tuple[float, float]:
        """Calculate support and resistance levels using local minima/maxima"""
        if len(prices) < 20:
            return prices.min(), prices.max()
        
        # Use rolling windows to find local extremes
        window = min(10, len(prices) // 4)
        
        local_mins = prices.rolling(window=window, center=True).min()
        local_maxs = prices.rolling(window=window, center=True).max()
        
        # Find support (average of recent local minima)
        support_candidates = prices[prices == local_mins].tail(5)
        support = support_candidates.mean() if len(support_candidates) > 0 else prices.min()
        
        # Find resistance (average of recent local maxima)
        resistance_candidates = prices[prices == local_maxs].tail(5)
        resistance = resistance_candidates.mean() if len(resistance_candidates) > 0 else prices.max()
        
        return support, resistance
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI (Relative Strength Index)"""
        if len(prices) < period:
            return 50.0
        
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    
    def calculate_momentum(self, prices: pd.Series, period: int = 10) -> float:
        """Calculate price momentum"""
        if len(prices) < period:
            return 0.0
        
        current_price = prices.iloc[-1]
        past_price = prices.iloc[-period]
        
        momentum = (current_price - past_price) / past_price
        return momentum
    
    def classify_market_regime(self, analysis: MarketAnalysis) -> MarketRegime:
        """Classify market regime based on analysis"""
        
        # High volatility regime
        if analysis.volatility > 0.5:
            return MarketRegime.VOLATILE
        
        # Trending regimes
        if abs(analysis.trend_strength) > 0.3:
            if analysis.trend_strength > 0:
                return MarketRegime.TRENDING_UP
            else:
                return MarketRegime.TRENDING_DOWN
        
        # Sideways regime
        if abs(analysis.trend_strength) < 0.1 and analysis.volatility < 0.2:
            return MarketRegime.SIDEWAYS
        
        return MarketRegime.UNKNOWN
    
    def analyze_market(self, prices: pd.Series) -> MarketAnalysis:
        """Perform comprehensive market analysis"""
        
        if len(prices) < 10:
            return MarketAnalysis(
                regime=MarketRegime.UNKNOWN,
                volatility=0.0,
                trend_strength=0.0,
                support_level=prices.min() if len(prices) > 0 else 0.0,
                resistance_level=prices.max() if len(prices) > 0 else 0.0,
                rsi=50.0,
                momentum=0.0,
                confidence=0.0
            )
        
        # Calculate all indicators
        volatility = self.calculate_volatility(prices)
        trend_strength = self.calculate_trend_strength(prices)
        support, resistance = self.calculate_support_resistance(prices)
        rsi = self.calculate_rsi(prices)
        momentum = self.calculate_momentum(prices)
        
        # Create analysis object
        analysis = MarketAnalysis(
            regime=MarketRegime.UNKNOWN,  # Will be set below
            volatility=volatility,
            trend_strength=trend_strength,
            support_level=support,
            resistance_level=resistance,
            rsi=rsi,
            momentum=momentum,
            confidence=min(len(prices) / self.lookback_period, 1.0)
        )
        
        # Classify regime
        analysis.regime = self.classify_market_regime(analysis)
        
        return analysis


class StrategyOptimizer:
    """
    Optimizes trading strategy parameters based on market conditions and performance.
    """
    
    def __init__(self):
        self.performance_history = []
        
    def optimize_for_regime(self, regime: MarketRegime, analysis: MarketAnalysis) -> Dict[str, Any]:
        """Optimize strategy parameters for specific market regime"""
        
        base_params = {
            'short_window': 10,
            'long_window': 30,
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'stop_loss_pct': 2.0,
            'take_profit_pct': 5.0
        }
        
        if regime == MarketRegime.TRENDING_UP:
            # More aggressive in uptrends
            base_params.update({
                'short_window': 5,
                'long_window': 20,
                'rsi_oversold': 40,
                'take_profit_pct': 8.0,
                'stop_loss_pct': 3.0
            })
            
        elif regime == MarketRegime.TRENDING_DOWN:
            # More conservative in downtrends
            base_params.update({
                'short_window': 15,
                'long_window': 40,
                'rsi_overbought': 60,
                'take_profit_pct': 3.0,
                'stop_loss_pct': 1.5
            })
            
        elif regime == MarketRegime.SIDEWAYS:
            # Range trading parameters
            base_params.update({
                'short_window': 8,
                'long_window': 25,
                'rsi_oversold': 25,
                'rsi_overbought': 75,
                'take_profit_pct': 2.0,
                'stop_loss_pct': 1.0
            })
            
        elif regime == MarketRegime.VOLATILE:
            # Wider stops and targets for volatile markets
            base_params.update({
                'short_window': 12,
                'long_window': 35,
                'rsi_oversold': 20,
                'rsi_overbought': 80,
                'take_profit_pct': 10.0,
                'stop_loss_pct': 5.0
            })
        
        # Adjust based on volatility
        volatility_multiplier = 1 + analysis.volatility
        base_params['stop_loss_pct'] *= volatility_multiplier
        base_params['take_profit_pct'] *= volatility_multiplier
        
        return base_params
    
    def calculate_position_size(self, analysis: MarketAnalysis, balance: float, risk_per_trade: float = 0.02) -> float:
        """Calculate optimal position size based on risk management"""
        
        # Base position size as percentage of balance
        base_position_pct = 0.1  # 10% of balance
        
        # Adjust based on confidence
        confidence_multiplier = analysis.confidence
        
        # Adjust based on volatility (lower position size for higher volatility)
        volatility_adjustment = 1 / (1 + analysis.volatility)
        
        # Calculate final position percentage
        position_pct = base_position_pct * confidence_multiplier * volatility_adjustment
        
        # Ensure position size respects risk management
        max_position_value = balance * position_pct
        
        return max_position_value


class AITradingAgent:
    """
    Main AI agent that makes trading decisions based on market analysis and strategy optimization.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.analyzer = MarketAnalyzer()
        self.optimizer = StrategyOptimizer()
        
        # Agent state
        self.last_analysis = None
        self.last_decision = None
        self.decision_history = []
        
        # Learning parameters
        self.learning_rate = config.get('learning_rate', 0.01)
        self.exploration_rate = config.get('exploration_rate', 0.1)
        
        logger.info("AI Trading Agent initialized")
    
    def analyze_market_conditions(self, price_data: pd.Series, volume_data: Optional[pd.Series] = None) -> MarketAnalysis:
        """Analyze current market conditions"""
        
        analysis = self.analyzer.analyze_market(price_data)
        self.last_analysis = analysis
        
        logger.info(f"Market Analysis: Regime={analysis.regime.value}, "
                   f"Volatility={analysis.volatility:.3f}, "
                   f"Trend={analysis.trend_strength:.3f}, "
                   f"RSI={analysis.rsi:.1f}, "
                   f"Confidence={analysis.confidence:.3f}")
        
        return analysis
    
    def make_trading_decision(self, 
                            current_price: float, 
                            price_history: pd.Series, 
                            current_position: Optional[Dict],
                            balance: float) -> TradingDecision:
        """Make an intelligent trading decision"""
        
        # Analyze market conditions
        analysis = self.analyze_market_conditions(price_history)
        
        # Get optimized strategy parameters
        strategy_params = self.optimizer.optimize_for_regime(analysis.regime, analysis)
        
        # Determine trading signal
        signal = self._determine_signal(analysis, current_position, current_price)
        
        # Calculate position size
        position_value = self.optimizer.calculate_position_size(analysis, balance)
        position_size = position_value / current_price if current_price > 0 else 0
        
        # Calculate stop loss and take profit
        stop_loss = None
        take_profit = None
        
        if signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
            stop_loss = current_price * (1 - strategy_params['stop_loss_pct'] / 100)
            take_profit = current_price * (1 + strategy_params['take_profit_pct'] / 100)
        elif signal in [TradingSignal.SELL, TradingSignal.STRONG_SELL] and current_position:
            entry_price = current_position.get('price', current_price)
            stop_loss = entry_price * (1 + strategy_params['stop_loss_pct'] / 100)
            take_profit = entry_price * (1 - strategy_params['take_profit_pct'] / 100)
        
        # Generate reasoning
        reasoning = self._generate_reasoning(analysis, signal, strategy_params)
        
        # Create decision
        decision = TradingDecision(
            signal=signal,
            confidence=analysis.confidence,
            reasoning=reasoning,
            suggested_position_size=position_size,
            stop_loss=stop_loss,
            take_profit=take_profit,
            strategy_params=strategy_params
        )
        
        self.last_decision = decision
        self.decision_history.append({
            'timestamp': datetime.now(),
            'decision': decision,
            'analysis': analysis,
            'price': current_price
        })
        
        logger.info(f"Trading Decision: {signal.value} (confidence: {analysis.confidence:.3f})")
        logger.info(f"Reasoning: {reasoning}")
        
        return decision
    
    def _determine_signal(self, analysis: MarketAnalysis, current_position: Optional[Dict], current_price: float) -> TradingSignal:
        """Determine trading signal based on analysis"""
        
        # If we have a position, consider exit signals
        if current_position:
            entry_price = current_position.get('price', current_price)
            profit_pct = ((current_price - entry_price) / entry_price) * 100
            
            # Strong sell signals
            if (analysis.rsi > 80 and analysis.trend_strength < -0.2) or profit_pct < -5:
                return TradingSignal.STRONG_SELL
            
            # Regular sell signals
            if analysis.rsi > 70 or profit_pct > 5 or current_price >= analysis.resistance_level:
                return TradingSignal.SELL
            
            # Hold position
            return TradingSignal.HOLD
        
        else:
            # No position, consider entry signals
            
            # Strong buy signals
            if (analysis.regime == MarketRegime.TRENDING_UP and 
                analysis.rsi < 30 and 
                analysis.momentum > 0.02 and
                current_price <= analysis.support_level * 1.02):
                return TradingSignal.STRONG_BUY
            
            # Regular buy signals
            if (analysis.rsi < 40 and 
                analysis.trend_strength > 0.1 and
                current_price <= analysis.support_level * 1.05):
                return TradingSignal.BUY
            
            # No clear signal
            return TradingSignal.HOLD
    
    def _generate_reasoning(self, analysis: MarketAnalysis, signal: TradingSignal, params: Dict) -> str:
        """Generate human-readable reasoning for the decision"""
        
        reasoning_parts = []
        
        # Market regime
        reasoning_parts.append(f"Market regime: {analysis.regime.value}")
        
        # Key indicators
        reasoning_parts.append(f"RSI: {analysis.rsi:.1f}")
        reasoning_parts.append(f"Trend strength: {analysis.trend_strength:.3f}")
        reasoning_parts.append(f"Volatility: {analysis.volatility:.3f}")
        
        # Signal-specific reasoning
        if signal == TradingSignal.STRONG_BUY:
            reasoning_parts.append("Strong bullish signals detected")
        elif signal == TradingSignal.BUY:
            reasoning_parts.append("Moderate bullish signals detected")
        elif signal == TradingSignal.SELL:
            reasoning_parts.append("Bearish signals or profit target reached")
        elif signal == TradingSignal.STRONG_SELL:
            reasoning_parts.append("Strong bearish signals or stop loss triggered")
        else:
            reasoning_parts.append("No clear directional signals")
        
        return "; ".join(reasoning_parts)
    
    def update_performance(self, trade_result: Dict):
        """Update agent performance based on trade results"""
        
        # Store performance data for learning
        self.optimizer.performance_history.append({
            'timestamp': datetime.now(),
            'trade_result': trade_result,
            'decision': self.last_decision,
            'analysis': self.last_analysis
        })
        
        # Simple learning: adjust exploration rate based on performance
        if trade_result.get('profit_pct', 0) > 0:
            self.exploration_rate *= 0.99  # Reduce exploration on success
        else:
            self.exploration_rate *= 1.01  # Increase exploration on failure
        
        # Keep exploration rate within bounds
        self.exploration_rate = max(0.05, min(0.3, self.exploration_rate))
        
        logger.info(f"Performance updated. Exploration rate: {self.exploration_rate:.3f}")
    
    def get_agent_status(self) -> Dict:
        """Get current agent status"""
        
        return {
            'last_analysis': {
                'regime': self.last_analysis.regime.value if self.last_analysis else 'unknown',
                'volatility': self.last_analysis.volatility if self.last_analysis else 0.0,
                'trend_strength': self.last_analysis.trend_strength if self.last_analysis else 0.0,
                'rsi': self.last_analysis.rsi if self.last_analysis else 50.0,
                'confidence': self.last_analysis.confidence if self.last_analysis else 0.0
            },
            'last_decision': {
                'signal': self.last_decision.signal.value if self.last_decision else 'hold',
                'confidence': self.last_decision.confidence if self.last_decision else 0.0,
                'reasoning': self.last_decision.reasoning if self.last_decision else 'No decision made'
            },
            'exploration_rate': self.exploration_rate,
            'total_decisions': len(self.decision_history),
            'performance_records': len(self.optimizer.performance_history)
        }


# Example usage and testing
async def test_ai_agent():
    """Test the AI agent functionality"""
    
    config = {
        'learning_rate': 0.01,
        'exploration_rate': 0.1
    }
    
    agent = AITradingAgent(config)
    
    # Generate mock price data
    np.random.seed(42)
    base_price = 50000
    prices = [base_price]
    
    for i in range(100):
        change = np.random.normal(0, 0.02)  # 2% daily volatility
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    price_series = pd.Series(prices)
    
    # Test market analysis
    print("=== AI Agent Test ===")
    analysis = agent.analyze_market_conditions(price_series)
    print(f"Market Analysis: {analysis}")
    
    # Test trading decision
    decision = agent.make_trading_decision(
        current_price=prices[-1],
        price_history=price_series,
        current_position=None,
        balance=10000
    )
    
    print(f"Trading Decision: {decision}")
    
    # Test with position
    mock_position = {'price': prices[-10], 'amount': 0.1}
    decision_with_position = agent.make_trading_decision(
        current_price=prices[-1],
        price_history=price_series,
        current_position=mock_position,
        balance=10000
    )
    
    print(f"Decision with Position: {decision_with_position}")
    
    # Test performance update
    mock_trade_result = {'profit_pct': 2.5, 'profit_loss': 125}
    agent.update_performance(mock_trade_result)
    
    # Get agent status
    status = agent.get_agent_status()
    print(f"Agent Status: {status}")


if __name__ == "__main__":
    asyncio.run(test_ai_agent())

