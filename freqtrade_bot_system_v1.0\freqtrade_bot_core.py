#!/usr/bin/env python3
"""
Freqtrade Bot System - Core Bot Implementation
A simplified Freqtrade-compatible trading bot with basic buy low, sell high strategy.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import ccxt
import pandas as pd
import numpy as np

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TradingStrategy:
    """
    Basic buy low, sell high trading strategy.
    Uses simple moving averages and RSI for decision making.
    """
    
    def __init__(self, short_window: int = 10, long_window: int = 30, rsi_period: int = 14):
        self.short_window = short_window
        self.long_window = long_window
        self.rsi_period = rsi_period
        
    def calculate_rsi(self, prices: pd.Series) -> float:
        """Calculate RSI (Relative Strength Index)"""
        if len(prices) < self.rsi_period:
            return 50.0  # Neutral RSI if not enough data
            
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50.0
    
    def should_buy(self, prices: pd.Series) -> Tuple[bool, str]:
        """Determine if we should buy based on strategy"""
        if len(prices) < self.long_window:
            return False, "Not enough data for analysis"
            
        # Calculate moving averages
        short_ma = prices.rolling(window=self.short_window).mean().iloc[-1]
        long_ma = prices.rolling(window=self.long_window).mean().iloc[-1]
        
        # Calculate RSI
        rsi = self.calculate_rsi(prices)
        
        # Buy conditions: short MA crosses above long MA and RSI indicates oversold
        if short_ma > long_ma and rsi < 30:
            return True, f"Buy signal: SMA({self.short_window})={short_ma:.4f} > LMA({self.long_window})={long_ma:.4f}, RSI={rsi:.2f}"
        
        return False, f"No buy signal: SMA={short_ma:.4f}, LMA={long_ma:.4f}, RSI={rsi:.2f}"
    
    def should_sell(self, prices: pd.Series, entry_price: float) -> Tuple[bool, str]:
        """Determine if we should sell based on strategy"""
        if len(prices) < self.long_window:
            return False, "Not enough data for analysis"
            
        current_price = prices.iloc[-1]
        
        # Calculate moving averages
        short_ma = prices.rolling(window=self.short_window).mean().iloc[-1]
        long_ma = prices.rolling(window=self.long_window).mean().iloc[-1]
        
        # Calculate RSI
        rsi = self.calculate_rsi(prices)
        
        # Calculate profit/loss percentage
        profit_pct = ((current_price - entry_price) / entry_price) * 100
        
        # Sell conditions: 
        # 1. Take profit at 5% gain
        # 2. Stop loss at 2% loss
        # 3. Short MA crosses below long MA and RSI indicates overbought
        if profit_pct >= 5.0:
            return True, f"Take profit: {profit_pct:.2f}% gain"
        elif profit_pct <= -2.0:
            return True, f"Stop loss: {profit_pct:.2f}% loss"
        elif short_ma < long_ma and rsi > 70:
            return True, f"Sell signal: SMA({self.short_window})={short_ma:.4f} < LMA({self.long_window})={long_ma:.4f}, RSI={rsi:.2f}"
        
        return False, f"Hold position: profit={profit_pct:.2f}%, SMA={short_ma:.4f}, LMA={long_ma:.4f}, RSI={rsi:.2f}"


class FreqtradeBotCore:
    """
    Core Freqtrade-compatible bot implementation.
    Handles market data, strategy execution, and basic order management.
    """
    
    def __init__(self, config: Dict):
        self.config = config
        self.exchange_name = config.get('exchange', 'binance')
        self.trading_pair = config.get('trading_pair', 'BTC/USDT')
        self.timeframe = config.get('timeframe', '1m')
        self.initial_balance = config.get('initial_balance', 1000.0)
        
        # Initialize exchange (using paper trading by default)
        self.exchange = self._initialize_exchange()
        
        # Initialize strategy
        self.strategy = TradingStrategy(
            short_window=config.get('short_window', 10),
            long_window=config.get('long_window', 30),
            rsi_period=config.get('rsi_period', 14)
        )
        
        # Bot state
        self.is_running = False
        self.current_position = None  # {'side': 'buy', 'amount': 0.1, 'price': 50000, 'timestamp': datetime}
        self.balance = self.initial_balance
        self.trade_history = []
        self.price_history = pd.Series(dtype=float)
        
        logger.info(f"FreqtradeBotCore initialized for {self.trading_pair} on {self.exchange_name}")
    
    def _initialize_exchange(self) -> ccxt.Exchange:
        """Initialize the exchange connection"""
        try:
            exchange_class = getattr(ccxt, self.exchange_name.lower())
            exchange = exchange_class({
                'apiKey': self.config.get('api_key', ''),
                'secret': self.config.get('api_secret', ''),
                'sandbox': self.config.get('sandbox', True),  # Use sandbox by default
                'enableRateLimit': True,
            })
            
            # Test connection
            exchange.load_markets()
            logger.info(f"Successfully connected to {self.exchange_name}")
            return exchange
            
        except Exception as e:
            logger.error(f"Failed to initialize exchange {self.exchange_name}: {e}")
            # Fallback to a mock exchange for development
            return self._create_mock_exchange()
    
    def _create_mock_exchange(self):
        """Create a mock exchange for testing purposes"""
        class MockExchange:
            def __init__(self):
                self.markets = {'BTC/USDT': {'symbol': 'BTC/USDT'}}
                self.base_price = 50000.0
                
            def load_markets(self):
                return self.markets
                
            def fetch_ticker(self, symbol):
                # Simulate price movement
                import random
                price_change = random.uniform(-0.02, 0.02)  # ±2% random movement
                self.base_price *= (1 + price_change)
                return {
                    'symbol': symbol,
                    'last': self.base_price,
                    'bid': self.base_price * 0.999,
                    'ask': self.base_price * 1.001,
                    'timestamp': int(time.time() * 1000)
                }
                
            def fetch_ohlcv(self, symbol, timeframe='1m', limit=100):
                # Generate mock OHLCV data
                data = []
                base_time = int(time.time() * 1000) - (limit * 60 * 1000)  # 1 minute intervals
                
                for i in range(limit):
                    timestamp = base_time + (i * 60 * 1000)
                    price = self.base_price * (1 + np.random.uniform(-0.01, 0.01))
                    data.append([
                        timestamp,
                        price,  # open
                        price * 1.005,  # high
                        price * 0.995,  # low
                        price * (1 + np.random.uniform(-0.005, 0.005)),  # close
                        np.random.uniform(10, 100)  # volume
                    ])
                
                return data
        
        logger.warning("Using mock exchange for development/testing")
        return MockExchange()
    
    async def fetch_market_data(self) -> Optional[float]:
        """Fetch current market price"""
        try:
            ticker = self.exchange.fetch_ticker(self.trading_pair)
            current_price = ticker['last']
            
            # Update price history
            current_time = pd.Timestamp.now()
            self.price_history = pd.concat([
                self.price_history,
                pd.Series([current_price], index=[current_time])
            ])
            
            # Keep only last 100 data points to manage memory
            if len(self.price_history) > 100:
                self.price_history = self.price_history.tail(100)
            
            return current_price
            
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            return None
    
    def execute_buy_order(self, price: float, amount: float) -> bool:
        """Execute a buy order"""
        try:
            cost = price * amount
            if cost > self.balance:
                logger.warning(f"Insufficient balance: need {cost}, have {self.balance}")
                return False
            
            # For mock trading, just update internal state
            self.current_position = {
                'side': 'buy',
                'amount': amount,
                'price': price,
                'timestamp': datetime.now()
            }
            
            self.balance -= cost
            
            trade_record = {
                'timestamp': datetime.now(),
                'side': 'buy',
                'amount': amount,
                'price': price,
                'cost': cost
            }
            self.trade_history.append(trade_record)
            
            logger.info(f"BUY executed: {amount} {self.trading_pair} at {price}, cost: {cost}")
            return True
            
        except Exception as e:
            logger.error(f"Error executing buy order: {e}")
            return False
    
    def execute_sell_order(self, price: float, amount: float) -> bool:
        """Execute a sell order"""
        try:
            if not self.current_position or self.current_position['side'] != 'buy':
                logger.warning("No position to sell")
                return False
            
            # For mock trading, just update internal state
            proceeds = price * amount
            self.balance += proceeds
            
            # Calculate profit/loss
            entry_price = self.current_position['price']
            profit_loss = (price - entry_price) * amount
            profit_pct = ((price - entry_price) / entry_price) * 100
            
            trade_record = {
                'timestamp': datetime.now(),
                'side': 'sell',
                'amount': amount,
                'price': price,
                'proceeds': proceeds,
                'profit_loss': profit_loss,
                'profit_pct': profit_pct
            }
            self.trade_history.append(trade_record)
            
            # Clear position
            self.current_position = None
            
            logger.info(f"SELL executed: {amount} {self.trading_pair} at {price}, proceeds: {proceeds}, P&L: {profit_loss:.2f} ({profit_pct:.2f}%)")
            return True
            
        except Exception as e:
            logger.error(f"Error executing sell order: {e}")
            return False
    
    async def trading_loop(self):
        """Main trading loop"""
        logger.info("Starting trading loop...")
        
        while self.is_running:
            try:
                # Fetch current market data
                current_price = await self.fetch_market_data()
                if current_price is None:
                    await asyncio.sleep(10)
                    continue
                
                logger.info(f"Current price: {current_price}")
                
                # Check if we have enough data for strategy
                if len(self.price_history) < self.strategy.long_window:
                    logger.info(f"Collecting data... {len(self.price_history)}/{self.strategy.long_window}")
                    await asyncio.sleep(10)
                    continue
                
                # Execute trading logic
                if self.current_position is None:
                    # Look for buy signals
                    should_buy, reason = self.strategy.should_buy(self.price_history)
                    logger.info(f"Buy analysis: {reason}")
                    
                    if should_buy:
                        # Calculate position size (use 10% of balance)
                        position_value = self.balance * 0.1
                        amount = position_value / current_price
                        
                        if self.execute_buy_order(current_price, amount):
                            logger.info(f"Position opened: {amount} at {current_price}")
                
                else:
                    # Look for sell signals
                    entry_price = self.current_position['price']
                    should_sell, reason = self.strategy.should_sell(self.price_history, entry_price)
                    logger.info(f"Sell analysis: {reason}")
                    
                    if should_sell:
                        amount = self.current_position['amount']
                        if self.execute_sell_order(current_price, amount):
                            logger.info(f"Position closed: {amount} at {current_price}")
                
                # Log current status
                total_value = self.balance
                if self.current_position:
                    total_value += self.current_position['amount'] * current_price
                
                logger.info(f"Balance: {self.balance:.2f}, Total Value: {total_value:.2f}, Trades: {len(self.trade_history)}")
                
                # Wait before next iteration
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(10)
    
    def start(self):
        """Start the trading bot"""
        self.is_running = True
        logger.info("FreqtradeBotCore started")
    
    def stop(self):
        """Stop the trading bot"""
        self.is_running = False
        logger.info("FreqtradeBotCore stopped")
    
    def get_status(self) -> Dict:
        """Get current bot status"""
        current_price = 0
        if len(self.price_history) > 0:
            current_price = self.price_history.iloc[-1]
        
        total_value = self.balance
        if self.current_position:
            total_value += self.current_position['amount'] * current_price
        
        return {
            'is_running': self.is_running,
            'trading_pair': self.trading_pair,
            'current_price': current_price,
            'balance': self.balance,
            'total_value': total_value,
            'current_position': self.current_position,
            'total_trades': len(self.trade_history),
            'profit_loss': total_value - self.initial_balance,
            'profit_pct': ((total_value - self.initial_balance) / self.initial_balance) * 100
        }


async def main():
    """Main function for testing the bot"""
    config = {
        'exchange': 'binance',
        'trading_pair': 'BTC/USDT',
        'timeframe': '1m',
        'initial_balance': 1000.0,
        'sandbox': True,
        'short_window': 10,
        'long_window': 30,
        'rsi_period': 14
    }
    
    bot = FreqtradeBotCore(config)
    bot.start()
    
    try:
        # Run for a limited time for testing
        await asyncio.wait_for(bot.trading_loop(), timeout=300)  # 5 minutes
    except asyncio.TimeoutError:
        logger.info("Test run completed")
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    finally:
        bot.stop()
        
        # Print final status
        status = bot.get_status()
        print("\n=== Final Status ===")
        for key, value in status.items():
            print(f"{key}: {value}")


if __name__ == "__main__":
    asyncio.run(main())

