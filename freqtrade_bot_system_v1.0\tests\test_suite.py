#!/usr/bin/env python3
"""
Comprehensive Test Suite for Freqtrade Bot System
Tests all components: Core bots, AI agents, Telegram integration, Bisq integration, and parallel execution.
"""

import asyncio
import unittest
import tempfile
import shutil
import time
import json
import os
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

# Import our modules
from freqtrade_bot_core import FreqtradeBot<PERSON>ore
from ai_agent import AITradingAgent
from ai_enhanced_bot import AIEnhancedFreqtradeBot
from telegram_bot import BotManager
from bisq_integration import BisqAPIClient, BisqIntegrationModule
from parallel_execution import ResourceManager, ProcessManager, ParallelExecutionSystem
from master_control import MasterControlSystem, SystemConfig


class TestFreqtradeBotCore(unittest.TestCase):
    """Test cases for FreqtradeBotCore"""
    
    def setUp(self):
        self.config = {
            'exchange': 'binance',
            'trading_pair': 'BTC/USDT',
            'timeframe': '1m',
            'initial_balance': 1000.0,
            'sandbox': True
        }
        self.bot = FreqtradeBotCore(self.config)
    
    def test_initialization(self):
        """Test bot initialization"""
        self.assertEqual(self.bot.trading_pair, 'BTC/USDT')
        self.assertEqual(self.bot.balance, 1000.0)
        self.assertFalse(self.bot.is_running)
        self.assertEqual(len(self.bot.price_history), 0)
    
    def test_start_stop(self):
        """Test bot start and stop functionality"""
        self.bot.start()
        self.assertTrue(self.bot.is_running)
        
        self.bot.stop()
        self.assertFalse(self.bot.is_running)
    
    @patch('ccxt.binance')
    def test_fetch_price(self, mock_exchange):
        """Test price fetching"""
        # Mock exchange response
        mock_exchange.return_value.fetch_ticker.return_value = {
            'last': 50000.0,
            'timestamp': int(time.time() * 1000)
        }
        
        price = self.bot.fetch_current_price()
        self.assertEqual(price, 50000.0)
        self.assertEqual(len(self.bot.price_history), 1)
    
    def test_buy_sell_logic(self):
        """Test buy and sell order logic"""
        # Test buy order
        success = self.bot.place_buy_order(0.1, 50000.0)
        self.assertTrue(success)
        self.assertEqual(self.bot.btc_balance, 0.1)
        self.assertEqual(self.bot.balance, 500.0)  # 1000 - (0.1 * 50000)
        
        # Test sell order
        success = self.bot.place_sell_order(0.05, 52000.0)
        self.assertTrue(success)
        self.assertEqual(self.bot.btc_balance, 0.05)
        self.assertEqual(self.bot.balance, 3100.0)  # 500 + (0.05 * 52000)
    
    def test_status_reporting(self):
        """Test status reporting"""
        status = self.bot.get_status()
        
        required_fields = ['is_running', 'trading_pair', 'balance', 'btc_balance', 
                          'total_value', 'total_trades', 'current_price']
        
        for field in required_fields:
            self.assertIn(field, status)


class TestAITradingAgent(unittest.TestCase):
    """Test cases for AI Trading Agent"""
    
    def setUp(self):
        self.config = {
            'learning_rate': 0.01,
            'exploration_rate': 0.1
        }
        self.agent = AITradingAgent(self.config)
    
    def test_initialization(self):
        """Test AI agent initialization"""
        self.assertEqual(self.agent.learning_rate, 0.01)
        self.assertEqual(self.agent.exploration_rate, 0.1)
        self.assertIsNotNone(self.agent.model)
    
    def test_market_analysis(self):
        """Test market analysis functionality"""
        # Create sample price data
        prices = [50000, 50100, 49900, 50200, 50050]
        volumes = [100, 120, 90, 110, 105]
        
        analysis = self.agent.analyze_market_conditions(prices, volumes)
        
        self.assertIn('regime', analysis)
        self.assertIn('volatility', analysis)
        self.assertIn('trend', analysis)
        self.assertIn('confidence', analysis)
    
    def test_decision_making(self):
        """Test trading decision making"""
        market_data = {
            'current_price': 50000,
            'rsi': 65,
            'sma_short': 49800,
            'sma_long': 49500,
            'volume': 1000
        }
        
        decision = self.agent.make_trading_decision(market_data)
        
        self.assertIn('signal', decision)
        self.assertIn('confidence', decision)
        self.assertIn('reasoning', decision)
        self.assertIn(decision['signal'], ['buy', 'sell', 'hold'])
    
    def test_learning_update(self):
        """Test learning from trade results"""
        trade_result = {
            'action': 'buy',
            'price': 50000,
            'profit': 100,
            'success': True
        }
        
        # Should not raise exception
        self.agent.update_from_trade_result(trade_result)


class TestTelegramBotManager(unittest.TestCase):
    """Test cases for Telegram Bot Manager"""
    
    def setUp(self):
        self.manager = BotManager()
    
    def test_bot_creation(self):
        """Test bot creation and management"""
        config = {
            'exchange': 'binance',
            'trading_pair': 'BTC/USDT',
            'initial_balance': 1000.0
        }
        
        success = self.manager.create_bot("test_bot", config)
        self.assertTrue(success)
        self.assertIn("test_bot", self.manager.bots)
    
    def test_bot_listing(self):
        """Test bot listing functionality"""
        # Create a few bots
        for i in range(3):
            config = {'trading_pair': f'BTC/USDT{i}'}
            self.manager.create_bot(f"bot_{i}", config)
        
        bot_list = self.manager.list_bots()
        self.assertEqual(len(bot_list), 3)
    
    @patch('asyncio.create_task')
    async def test_bot_start_stop(self, mock_create_task):
        """Test bot start and stop operations"""
        config = {'trading_pair': 'BTC/USDT'}
        self.manager.create_bot("test_bot", config)
        
        # Test start
        success = await self.manager.start_bot("test_bot")
        self.assertTrue(success)
        
        # Test stop
        success = await self.manager.stop_bot("test_bot")
        self.assertTrue(success)


class TestBisqIntegration(unittest.TestCase):
    """Test cases for Bisq Integration"""
    
    def setUp(self):
        self.client = BisqAPIClient(host="localhost", port=9998, password="test")
        self.config = {
            'bisq_host': 'localhost',
            'bisq_port': 9998,
            'bisq_password': 'test'
        }
        self.integration = BisqIntegrationModule(self.config)
    
    async def test_client_connection(self):
        """Test Bisq client connection"""
        success = await self.client.connect()
        self.assertTrue(success)
        self.assertTrue(self.client.connected)
        
        await self.client.disconnect()
        self.assertFalse(self.client.connected)
    
    async def test_offer_management(self):
        """Test offer creation and management"""
        await self.client.connect()
        
        # Test offer creation
        offer_id = await self.client.create_offer(
            direction="BUY",
            currency_code="USD",
            amount=*********,  # 1 BTC in satoshis
            min_amount=********,
            price="50000.00",
            payment_account_id="test_account"
        )
        
        self.assertIsNotNone(offer_id)
        
        # Test offer cancellation
        success = await self.client.cancel_offer(offer_id)
        self.assertTrue(success)
        
        await self.client.disconnect()
    
    async def test_integration_module(self):
        """Test Bisq integration module"""
        success = await self.integration.initialize()
        self.assertTrue(success)
        
        # Test order creation
        order_id = await self.integration.create_buy_order(0.1, 50000.0)
        self.assertIsNotNone(order_id)
        
        # Test order cancellation
        success = await self.integration.cancel_order(order_id)
        self.assertTrue(success)
        
        await self.integration.shutdown()


class TestParallelExecution(unittest.TestCase):
    """Test cases for Parallel Execution System"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.resource_manager = ResourceManager(max_instances=5)
        self.process_manager = ProcessManager(work_dir=self.temp_dir)
        self.parallel_system = ParallelExecutionSystem(max_instances=5)
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_resource_manager(self):
        """Test resource management"""
        can_start, reason = self.resource_manager.can_start_instance(0)
        self.assertTrue(can_start)
        
        # Test resource allocation
        allocation = self.resource_manager.get_resource_allocation(3)
        self.assertIn('cpu_limit', allocation)
        self.assertIn('memory_limit', allocation)
    
    def test_bot_instance_creation(self):
        """Test bot instance creation and management"""
        config = {
            'trading_pair': 'BTC/USDT',
            'update_interval': 5
        }
        
        bot_id = self.parallel_system.create_bot_instance("test_bot", config)
        self.assertIsNotNone(bot_id)
        self.assertIn(bot_id, self.parallel_system.instances)
    
    async def test_bot_lifecycle(self):
        """Test complete bot lifecycle"""
        config = {'trading_pair': 'BTC/USDT', 'update_interval': 1}
        
        # Create bot
        bot_id = self.parallel_system.create_bot_instance("test_bot", config)
        
        # Start bot
        success, message = self.parallel_system.start_bot(bot_id)
        self.assertTrue(success)
        
        # Wait a bit
        await asyncio.sleep(3)
        
        # Check status
        status = self.parallel_system.get_bot_status(bot_id)
        self.assertIsNotNone(status)
        self.assertEqual(status['name'], 'test_bot')
        
        # Stop bot
        success, message = self.parallel_system.stop_bot(bot_id)
        self.assertTrue(success)
        
        # Remove bot
        success, message = self.parallel_system.remove_bot(bot_id)
        self.assertTrue(success)
    
    async def test_monitoring_system(self):
        """Test monitoring functionality"""
        await self.parallel_system.start_monitoring()
        self.assertTrue(self.parallel_system.is_monitoring)
        
        await self.parallel_system.stop_monitoring()
        self.assertFalse(self.parallel_system.is_monitoring)


class TestMasterControlSystem(unittest.TestCase):
    """Test cases for Master Control System"""
    
    def setUp(self):
        self.config = SystemConfig(
            max_bot_instances=5,
            telegram_token="test_token",
            bisq_enabled=False,  # Disable for testing
            default_trading_config={
                'exchange': 'binance',
                'initial_balance': 1000.0
            }
        )
        self.master = MasterControlSystem(self.config)
    
    async def test_system_initialization(self):
        """Test master system initialization"""
        success = await self.master.initialize()
        self.assertTrue(success)
        self.assertTrue(self.master.is_running)
        
        await self.master.shutdown()
        self.assertFalse(self.master.is_running)
    
    async def test_integrated_bot_management(self):
        """Test integrated bot management"""
        await self.master.initialize()
        
        # Create bot
        bot_id = self.master.create_bot_with_config("test_bot", {"trading_pair": "BTC/USDT"})
        self.assertIsNotNone(bot_id)
        
        # Start bot
        success, message = self.master.start_bot(bot_id)
        self.assertTrue(success)
        
        # Get status
        status = self.master.get_bot_status(bot_id)
        self.assertIsNotNone(status)
        
        # Stop and remove
        self.master.stop_bot(bot_id)
        self.master.remove_bot(bot_id)
        
        await self.master.shutdown()
    
    async def test_telegram_commands(self):
        """Test Telegram command handling"""
        await self.master.initialize()
        
        # Test status command
        response = await self.master.handle_telegram_command("status", [])
        self.assertIn("System Overview", response)
        
        # Test create command
        response = await self.master.handle_telegram_command("create", ["test_bot", "BTC/USDT"])
        self.assertIn("Created bot", response)
        
        # Test list command
        response = await self.master.handle_telegram_command("list", [])
        self.assertIn("Bot List", response)
        
        await self.master.shutdown()


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    async def test_end_to_end_workflow(self):
        """Test complete end-to-end workflow"""
        # Setup master system
        config = SystemConfig(
            max_bot_instances=3,
            bisq_enabled=False,
            default_trading_config={
                'exchange': 'binance',
                'initial_balance': 1000.0,
                'sandbox': True
            }
        )
        
        master = MasterControlSystem(config)
        await master.initialize()
        
        try:
            # Create multiple bots
            bot_ids = []
            for i in range(3):
                bot_id = master.create_bot_with_config(
                    f"bot_{i}", 
                    {"trading_pair": f"BTC/USDT{i}"}
                )
                bot_ids.append(bot_id)
            
            # Start all bots
            for bot_id in bot_ids:
                success, _ = master.start_bot(bot_id)
                self.assertTrue(success)
            
            # Wait for initialization
            await asyncio.sleep(5)
            
            # Check system status
            overview = master.get_system_overview()
            self.assertEqual(overview['parallel_execution']['running_instances'], 3)
            
            # Test Telegram commands
            response = await master.handle_telegram_command("list", [])
            self.assertIn("bot_0", response)
            self.assertIn("bot_1", response)
            self.assertIn("bot_2", response)
            
            # Stop one bot
            success, _ = master.stop_bot(bot_ids[0])
            self.assertTrue(success)
            
            # Verify system state
            overview = master.get_system_overview()
            self.assertEqual(overview['parallel_execution']['running_instances'], 2)
            
        finally:
            await master.shutdown()


class TestPerformance(unittest.TestCase):
    """Performance tests"""
    
    async def test_bot_startup_time(self):
        """Test bot startup performance"""
        config = SystemConfig(max_bot_instances=10)
        master = MasterControlSystem(config)
        await master.initialize()
        
        try:
            start_time = time.time()
            
            # Create and start 5 bots
            bot_ids = []
            for i in range(5):
                bot_id = master.create_bot_with_config(f"perf_bot_{i}")
                bot_ids.append(bot_id)
                master.start_bot(bot_id)
            
            startup_time = time.time() - start_time
            
            # Should start 5 bots in under 10 seconds
            self.assertLess(startup_time, 10.0)
            
            # Cleanup
            for bot_id in bot_ids:
                master.stop_bot(bot_id)
                master.remove_bot(bot_id)
                
        finally:
            await master.shutdown()
    
    def test_memory_usage(self):
        """Test memory usage with multiple bots"""
        import psutil
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create system with multiple bots
        system = ParallelExecutionSystem(max_instances=10)
        
        # Create 5 bot instances
        for i in range(5):
            system.create_bot_instance(f"mem_test_{i}", {"trading_pair": "BTC/USDT"})
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for 5 bots)
        self.assertLess(memory_increase, 100)


def run_all_tests():
    """Run all test suites"""
    print("=== Running Freqtrade Bot System Test Suite ===\n")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestFreqtradeBotCore,
        TestAITradingAgent,
        TestTelegramBotManager,
        TestBisqIntegration,
        TestParallelExecution,
        TestMasterControlSystem,
        TestIntegration,
        TestPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n=== Test Summary ===")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print(f"\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()


async def run_async_tests():
    """Run async tests separately"""
    print("\n=== Running Async Tests ===\n")
    
    # Bisq integration tests
    print("Testing Bisq Integration...")
    bisq_test = TestBisqIntegration()
    bisq_test.setUp()
    
    try:
        await bisq_test.test_client_connection()
        await bisq_test.test_offer_management()
        await bisq_test.test_integration_module()
        print("✓ Bisq integration tests passed")
    except Exception as e:
        print(f"✗ Bisq integration tests failed: {e}")
    
    # Parallel execution tests
    print("\nTesting Parallel Execution...")
    parallel_test = TestParallelExecution()
    parallel_test.setUp()
    
    try:
        await parallel_test.test_bot_lifecycle()
        await parallel_test.test_monitoring_system()
        print("✓ Parallel execution tests passed")
    except Exception as e:
        print(f"✗ Parallel execution tests failed: {e}")
    finally:
        parallel_test.tearDown()
    
    # Master control tests
    print("\nTesting Master Control System...")
    master_test = TestMasterControlSystem()
    master_test.setUp()
    
    try:
        await master_test.test_system_initialization()
        await master_test.test_integrated_bot_management()
        await master_test.test_telegram_commands()
        print("✓ Master control tests passed")
    except Exception as e:
        print(f"✗ Master control tests failed: {e}")
    
    # Integration tests
    print("\nTesting End-to-End Integration...")
    integration_test = TestIntegration()
    
    try:
        await integration_test.test_end_to_end_workflow()
        print("✓ End-to-end integration tests passed")
    except Exception as e:
        print(f"✗ End-to-end integration tests failed: {e}")
    
    # Performance tests
    print("\nTesting Performance...")
    perf_test = TestPerformance()
    
    try:
        await perf_test.test_bot_startup_time()
        perf_test.test_memory_usage()
        print("✓ Performance tests passed")
    except Exception as e:
        print(f"✗ Performance tests failed: {e}")


if __name__ == "__main__":
    # Run synchronous tests
    sync_success = run_all_tests()
    
    # Run asynchronous tests
    print("\n" + "="*50)
    asyncio.run(run_async_tests())
    
    print(f"\n=== Overall Test Result ===")
    print(f"Synchronous tests: {'PASSED' if sync_success else 'FAILED'}")
    print("Asynchronous tests: See individual results above")
    print("\nTest suite execution complete!")

